import { BaseAdapter } from "./base-adapter.js";
import { MarketData, ForexData, StockData, HistoricalData, TimeInterval, TimePeriod } from "../types/market-data.js";
export declare class AlphaVantageAdapter extends BaseAdapter {
    private client;
    private readonly baseUrl;
    private readonly apiKey;
    constructor();
    getName(): string;
    isAvailable(): Promise<boolean>;
    initialize(): Promise<void>;
    getMarketData(symbol: string): Promise<MarketData>;
    getStockData(symbol: string): Promise<StockData>;
    getForexData(fromCurrency: string, toCurrency?: string): Promise<ForexData>;
    getHistoricalData(symbol: string, interval: TimeInterval, period: TimePeriod): Promise<HistoricalData>;
    private getFunctionName;
    private mapInterval;
    getEconomicIndicator(indicator: string, interval?: string): Promise<any>;
    getForexIntraday(fromCurrency: string, toCurrency: string, interval: TimeInterval): Promise<HistoricalData>;
}
//# sourceMappingURL=alpha-vantage.d.ts.map