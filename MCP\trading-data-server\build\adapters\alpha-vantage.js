import axios from "axios";
import { BaseAdapter } from "./base-adapter.js";
export class AlphaVantageAdapter extends BaseAdapter {
    client;
    baseUrl = "https://www.alphavantage.co/query";
    apiKey;
    constructor() {
        super("AlphaVantage", {
            maxRequests: 5,
            windowMs: 60000 // 5 requests per minute for free tier
        });
        this.apiKey = process.env.ALPHA_VANTAGE_API_KEY;
        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: 15000,
            headers: {
                'Accept': 'application/json'
            }
        });
    }
    getName() {
        return "Alpha Vantage";
    }
    async isAvailable() {
        if (!this.apiKey) {
            console.warn(`[${this.name}] API key not provided, service unavailable`);
            return false;
        }
        try {
            const response = await this.client.get('', {
                params: {
                    function: 'GLOBAL_QUOTE',
                    symbol: 'AAPL',
                    apikey: this.apiKey
                },
                timeout: 5000
            });
            return response.status === 200 && !response.data['Error Message'];
        }
        catch (error) {
            console.warn(`[${this.name}] Availability check failed:`, error);
            return false;
        }
    }
    async initialize() {
        console.log(`[${this.name}] Initializing...`);
        if (!this.apiKey) {
            console.warn(`[${this.name}] No API key provided - service will be unavailable`);
            return;
        }
        const available = await this.isAvailable();
        if (!available) {
            console.warn(`[${this.name}] Service may not be available`);
        }
        else {
            console.log(`[${this.name}] Successfully initialized`);
        }
    }
    async getMarketData(symbol) {
        this.validateSymbol(symbol);
        if (!this.apiKey) {
            throw new Error(`[${this.name}] API key not provided`);
        }
        return this.withRetry(async () => {
            const response = await this.client.get('', {
                params: {
                    function: 'GLOBAL_QUOTE',
                    symbol: symbol,
                    apikey: this.apiKey
                }
            });
            if (response.data['Error Message']) {
                throw new Error(`Alpha Vantage API Error: ${response.data['Error Message']}`);
            }
            const quote = response.data['Global Quote'];
            if (!quote) {
                throw new Error(`No data found for symbol: ${symbol}`);
            }
            const price = parseFloat(quote['05. price']);
            const change = parseFloat(quote['09. change']);
            const changePercent = parseFloat(quote['10. change percent'].replace('%', ''));
            const volume = parseInt(quote['06. volume']);
            return {
                symbol: symbol.toUpperCase(),
                price,
                timestamp: new Date().toISOString(), // Alpha Vantage nevracia presný timestamp
                volume,
                change,
                changePercent
            };
        });
    }
    async getStockData(symbol) {
        const marketData = await this.getMarketData(symbol);
        // Alpha Vantage má rozšírené dáta, ale vyžaduje ďalšie API volania
        // Pre základnú implementáciu vrátime len základné market data
        return {
            ...marketData,
            marketCap: undefined,
            pe: undefined,
            dividend: undefined,
            sector: undefined
        };
    }
    async getForexData(fromCurrency, toCurrency = 'USD') {
        if (!this.apiKey) {
            throw new Error(`[${this.name}] API key not provided`);
        }
        return this.withRetry(async () => {
            const response = await this.client.get('', {
                params: {
                    function: 'CURRENCY_EXCHANGE_RATE',
                    from_currency: fromCurrency,
                    to_currency: toCurrency,
                    apikey: this.apiKey
                }
            });
            if (response.data['Error Message']) {
                throw new Error(`Alpha Vantage API Error: ${response.data['Error Message']}`);
            }
            const exchangeRate = response.data['Realtime Currency Exchange Rate'];
            if (!exchangeRate) {
                throw new Error(`No forex data found for pair: ${fromCurrency}${toCurrency}`);
            }
            const price = parseFloat(exchangeRate['5. Exchange Rate']);
            const pair = `${fromCurrency}${toCurrency}`;
            return {
                symbol: pair,
                pair,
                price,
                timestamp: exchangeRate['6. Last Refreshed'],
                volume: undefined,
                change: undefined,
                changePercent: undefined,
                bid: parseFloat(exchangeRate['8. Bid Price']),
                ask: parseFloat(exchangeRate['9. Ask Price']),
                spread: undefined
            };
        });
    }
    async getHistoricalData(symbol, interval, period) {
        this.validateSymbol(symbol);
        this.validateTimeInterval(interval);
        if (!this.apiKey) {
            throw new Error(`[${this.name}] API key not provided`);
        }
        return this.withRetry(async () => {
            const functionName = this.getFunctionName(interval);
            const params = {
                function: functionName,
                symbol: symbol,
                apikey: this.apiKey
            };
            // Pre intraday dáta pridáme interval
            if (functionName === 'TIME_SERIES_INTRADAY') {
                params.interval = this.mapInterval(interval);
            }
            const response = await this.client.get('', { params });
            if (response.data['Error Message']) {
                throw new Error(`Alpha Vantage API Error: ${response.data['Error Message']}`);
            }
            // Nájdeme správny kľúč pre time series dáta
            const timeSeriesKey = Object.keys(response.data).find(key => key.includes('Time Series'));
            if (!timeSeriesKey) {
                throw new Error(`No historical data found for symbol: ${symbol}`);
            }
            const timeSeries = response.data[timeSeriesKey];
            const data = [];
            for (const [timestamp, values] of Object.entries(timeSeries)) {
                const ohlcv = values;
                data.push({
                    timestamp: new Date(timestamp).toISOString(),
                    open: parseFloat(ohlcv['1. open']),
                    high: parseFloat(ohlcv['2. high']),
                    low: parseFloat(ohlcv['3. low']),
                    close: parseFloat(ohlcv['4. close']),
                    volume: parseInt(ohlcv['5. volume'] || '0')
                });
            }
            // Zoradíme od najstarších po najnovšie
            data.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            return {
                symbol: symbol.toUpperCase(),
                data,
                interval,
                period
            };
        });
    }
    // Mapovanie intervalov na Alpha Vantage funkcie
    getFunctionName(interval) {
        if (['1m', '5m', '15m', '30m', '1h'].includes(interval)) {
            return 'TIME_SERIES_INTRADAY';
        }
        else if (interval === '1d') {
            return 'TIME_SERIES_DAILY';
        }
        else if (interval === '1w') {
            return 'TIME_SERIES_WEEKLY';
        }
        else if (interval === '1M') {
            return 'TIME_SERIES_MONTHLY';
        }
        return 'TIME_SERIES_DAILY';
    }
    // Mapovanie intervalov na Alpha Vantage formát
    mapInterval(interval) {
        const mapping = {
            "1m": "1min",
            "5m": "5min",
            "15m": "15min",
            "30m": "30min",
            "1h": "60min"
        };
        return mapping[interval] || "5min";
    }
    // Získanie ekonomických indikátorov (Alpha Vantage má rozsiahle ekonomické dáta)
    async getEconomicIndicator(indicator, interval = 'monthly') {
        if (!this.apiKey) {
            throw new Error(`[${this.name}] API key not provided`);
        }
        return this.withRetry(async () => {
            const response = await this.client.get('', {
                params: {
                    function: indicator,
                    interval: interval,
                    apikey: this.apiKey
                }
            });
            if (response.data['Error Message']) {
                throw new Error(`Alpha Vantage API Error: ${response.data['Error Message']}`);
            }
            return response.data;
        });
    }
    // Získanie forex intraday dát
    async getForexIntraday(fromCurrency, toCurrency, interval) {
        if (!this.apiKey) {
            throw new Error(`[${this.name}] API key not provided`);
        }
        return this.withRetry(async () => {
            const response = await this.client.get('', {
                params: {
                    function: 'FX_INTRADAY',
                    from_symbol: fromCurrency,
                    to_symbol: toCurrency,
                    interval: this.mapInterval(interval),
                    apikey: this.apiKey
                }
            });
            if (response.data['Error Message']) {
                throw new Error(`Alpha Vantage API Error: ${response.data['Error Message']}`);
            }
            const timeSeriesKey = Object.keys(response.data).find(key => key.includes('Time Series FX'));
            if (!timeSeriesKey) {
                throw new Error(`No forex data found for pair: ${fromCurrency}${toCurrency}`);
            }
            const timeSeries = response.data[timeSeriesKey];
            const data = [];
            for (const [timestamp, values] of Object.entries(timeSeries)) {
                const ohlc = values;
                data.push({
                    timestamp: new Date(timestamp).toISOString(),
                    open: parseFloat(ohlc['1. open']),
                    high: parseFloat(ohlc['2. high']),
                    low: parseFloat(ohlc['3. low']),
                    close: parseFloat(ohlc['4. close']),
                    volume: 0 // Forex nemá volume
                });
            }
            data.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            return {
                symbol: `${fromCurrency}${toCurrency}`,
                data,
                interval,
                period: '1d' // Alpha Vantage intraday poskytuje len posledný deň
            };
        });
    }
}
//# sourceMappingURL=alpha-vantage.js.map