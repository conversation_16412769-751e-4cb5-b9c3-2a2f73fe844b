{"version": 3, "file": "alpha-vantage.js", "sourceRoot": "", "sources": ["../../src/adapters/alpha-vantage.ts"], "names": [], "mappings": "AAAA,OAAO,KAAwB,MAAM,OAAO,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAGhD,MAAM,OAAO,mBAAoB,SAAQ,WAAW;IAC1C,MAAM,CAAgB;IACb,OAAO,GAAG,mCAAmC,CAAC;IAC9C,MAAM,CAAqB;IAE5C;QACE,KAAK,CAAC,cAAc,EAAE;YACpB,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,KAAK,CAAC,sCAAsC;SACvD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;QAEhD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,QAAQ,EAAE,kBAAkB;aAC7B;SACF,CAAC,CAAC;IACL,CAAC;IAED,OAAO;QACL,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,6CAA6C,CAAC,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE;gBACzC,MAAM,EAAE;oBACN,QAAQ,EAAE,cAAc;oBACxB,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB;gBACD,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,mBAAmB,CAAC,CAAC;QAE9C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,qDAAqD,CAAC,CAAC;YACjF,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,gCAAgC,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,4BAA4B,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc;QAChC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,wBAAwB,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE;gBACzC,MAAM,EAAE;oBACN,QAAQ,EAAE,cAAc;oBACxB,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,4BAA4B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;YAC7C,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;YAC/C,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;YAC/E,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;YAE7C,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,KAAK;gBACL,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,0CAA0C;gBAC/E,MAAM;gBACN,MAAM;gBACN,aAAa;aACd,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEpD,mEAAmE;QACnE,8DAA8D;QAC9D,OAAO;YACL,GAAG,UAAU;YACb,SAAS,EAAE,SAAS;YACpB,EAAE,EAAE,SAAS;YACb,QAAQ,EAAE,SAAS;YACnB,MAAM,EAAE,SAAS;SAClB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,YAAoB,EAAE,aAAqB,KAAK;QACjE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,wBAAwB,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE;gBACzC,MAAM,EAAE;oBACN,QAAQ,EAAE,wBAAwB;oBAClC,aAAa,EAAE,YAAY;oBAC3B,WAAW,EAAE,UAAU;oBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,4BAA4B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACtE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,iCAAiC,YAAY,GAAG,UAAU,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,MAAM,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC3D,MAAM,IAAI,GAAG,GAAG,YAAY,GAAG,UAAU,EAAE,CAAC;YAE5C,OAAO;gBACL,MAAM,EAAE,IAAI;gBACZ,IAAI;gBACJ,KAAK;gBACL,SAAS,EAAE,YAAY,CAAC,mBAAmB,CAAC;gBAC5C,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,SAAS;gBACjB,aAAa,EAAE,SAAS;gBACxB,GAAG,EAAE,UAAU,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC7C,GAAG,EAAE,UAAU,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC7C,MAAM,EAAE,SAAS;aAClB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,QAAsB,EAAE,MAAkB;QAChF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC5B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,wBAAwB,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,MAAM,GAAQ;gBAClB,QAAQ,EAAE,YAAY;gBACtB,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC;YAEF,qCAAqC;YACrC,IAAI,YAAY,KAAK,sBAAsB,EAAE,CAAC;gBAC5C,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAEvD,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,4BAA4B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,4CAA4C;YAC5C,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAC1D,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,CAC5B,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,wCAAwC,MAAM,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,IAAI,GAAgB,EAAE,CAAC;YAE7B,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7D,MAAM,KAAK,GAAG,MAAa,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC;oBACR,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;oBAC5C,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBAClC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBAClC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBAChC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;oBACpC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,uCAAuC;YACvC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAEvF,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,IAAI;gBACJ,QAAQ;gBACR,MAAM;aACP,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gDAAgD;IACxC,eAAe,CAAC,QAAsB;QAC5C,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxD,OAAO,sBAAsB,CAAC;QAChC,CAAC;aAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC7B,OAAO,mBAAmB,CAAC;QAC7B,CAAC;aAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC7B,OAAO,oBAAoB,CAAC;QAC9B,CAAC;aAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC7B,OAAO,qBAAqB,CAAC;QAC/B,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,+CAA+C;IACvC,WAAW,CAAC,QAAsB;QACxC,MAAM,OAAO,GAA2B;YACtC,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,OAAO;SACd,CAAC;QAEF,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrC,CAAC;IAED,iFAAiF;IACjF,KAAK,CAAC,oBAAoB,CAAC,SAAiB,EAAE,WAAmB,SAAS;QACxE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,wBAAwB,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE;gBACzC,MAAM,EAAE;oBACN,QAAQ,EAAE,SAAS;oBACnB,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,4BAA4B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8BAA8B;IAC9B,KAAK,CAAC,gBAAgB,CAAC,YAAoB,EAAE,UAAkB,EAAE,QAAsB;QACrF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,wBAAwB,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE;gBACzC,MAAM,EAAE;oBACN,QAAQ,EAAE,aAAa;oBACvB,WAAW,EAAE,YAAY;oBACzB,SAAS,EAAE,UAAU;oBACrB,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;oBACpC,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB;aACF,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,4BAA4B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAC1D,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAC/B,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,iCAAiC,YAAY,GAAG,UAAU,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,IAAI,GAAgB,EAAE,CAAC;YAE7B,KAAK,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,GAAG,MAAa,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC;oBACR,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;oBAC5C,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACjC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACjC,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC/B,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACnC,MAAM,EAAE,CAAC,CAAC,oBAAoB;iBAC/B,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAEvF,OAAO;gBACL,MAAM,EAAE,GAAG,YAAY,GAAG,UAAU,EAAE;gBACtC,IAAI;gBACJ,QAAQ;gBACR,MAAM,EAAE,IAAI,CAAC,oDAAoD;aAClE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}