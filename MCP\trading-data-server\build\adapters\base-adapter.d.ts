import { MarketData, HistoricalData, TimeInterval, TimePeriod } from "../types/market-data.js";
export interface RateLimitConfig {
    maxRequests: number;
    windowMs: number;
}
export declare abstract class BaseAdapter {
    protected name: string;
    protected rateLimitConfig: RateLimitConfig;
    private requestTimes;
    constructor(name: string, rateLimitConfig: RateLimitConfig);
    abstract getName(): string;
    abstract isAvailable(): Promise<boolean>;
    abstract initialize?(): Promise<void>;
    protected checkRateLimit(): Promise<void>;
    protected withRetry<T>(operation: () => Promise<T>, maxRetries?: number, baseDelay?: number): Promise<T>;
    protected validateSymbol(symbol: string): void;
    protected validateTimeInterval(interval: TimeInterval): void;
    protected validateTimePeriod(period: TimePeriod): void;
    abstract getMarketData(symbol: string): Promise<MarketData>;
    abstract getHistoricalData(symbol: string, interval: TimeInterval, period: TimePeriod): Promise<HistoricalData>;
}
//# sourceMappingURL=base-adapter.d.ts.map