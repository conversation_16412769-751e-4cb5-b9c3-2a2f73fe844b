{"version": 3, "file": "base-adapter.d.ts", "sourceRoot": "", "sources": ["../../src/adapters/base-adapter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAE/F,MAAM,WAAW,eAAe;IAC9B,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,8BAAsB,WAAW;IAC/B,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;IACvB,SAAS,CAAC,eAAe,EAAE,eAAe,CAAC;IAC3C,OAAO,CAAC,YAAY,CAAgB;gBAExB,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,eAAe;IAK1D,QAAQ,CAAC,OAAO,IAAI,MAAM;IAC1B,QAAQ,CAAC,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;IACxC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC;cAGrB,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC;cAuB/B,SAAS,CAAC,CAAC,EACzB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,UAAU,GAAE,MAAU,EACtB,SAAS,GAAE,MAAa,GACvB,OAAO,CAAC,CAAC,CAAC;IAyBb,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAO9C,SAAS,CAAC,oBAAoB,CAAC,QAAQ,EAAE,YAAY,GAAG,IAAI;IAQ5D,SAAS,CAAC,kBAAkB,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAQtD,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;IAC3D,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC;CAChH"}