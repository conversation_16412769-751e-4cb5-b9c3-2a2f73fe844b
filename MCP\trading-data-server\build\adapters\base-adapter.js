export class BaseAdapter {
    name;
    rateLimitConfig;
    requestTimes = [];
    constructor(name, rateLimitConfig) {
        this.name = name;
        this.rateLimitConfig = rateLimitConfig;
    }
    // Rate limiting
    async checkRateLimit() {
        const now = Date.now();
        const windowStart = now - this.rateLimitConfig.windowMs;
        // Odstráň staré requesty
        this.requestTimes = this.requestTimes.filter(time => time > windowStart);
        // Skontroluj limit
        if (this.requestTimes.length >= this.rateLimitConfig.maxRequests) {
            const oldestRequest = this.requestTimes[0];
            const waitTime = oldestRequest + this.rateLimitConfig.windowMs - now;
            if (waitTime > 0) {
                console.warn(`[${this.name}] Rate limit reached, waiting ${waitTime}ms`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
        // Pridaj nový request
        this.requestTimes.push(now);
    }
    // Error handling s retry logikou
    async withRetry(operation, maxRetries = 3, baseDelay = 1000) {
        let lastError;
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                await this.checkRateLimit();
                return await operation();
            }
            catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    break;
                }
                // Exponential backoff
                const delay = baseDelay * Math.pow(2, attempt);
                console.warn(`[${this.name}] Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        throw new Error(`[${this.name}] All ${maxRetries + 1} attempts failed. Last error: ${lastError?.message || 'Unknown error'}`);
    }
    // Validácia symbolov
    validateSymbol(symbol) {
        if (!symbol || typeof symbol !== 'string' || symbol.trim().length === 0) {
            throw new Error(`Invalid symbol: ${symbol}`);
        }
    }
    // Validácia časových intervalov
    validateTimeInterval(interval) {
        const validIntervals = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1M"];
        if (!validIntervals.includes(interval)) {
            throw new Error(`Invalid time interval: ${interval}. Valid intervals: ${validIntervals.join(', ')}`);
        }
    }
    // Validácia časových období
    validateTimePeriod(period) {
        const validPeriods = ["1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y", "10y", "ytd", "max"];
        if (!validPeriods.includes(period)) {
            throw new Error(`Invalid time period: ${period}. Valid periods: ${validPeriods.join(', ')}`);
        }
    }
}
//# sourceMappingURL=base-adapter.js.map