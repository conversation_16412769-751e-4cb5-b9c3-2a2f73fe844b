{"version": 3, "file": "base-adapter.js", "sourceRoot": "", "sources": ["../../src/adapters/base-adapter.ts"], "names": [], "mappings": "AAOA,MAAM,OAAgB,WAAW;IACrB,IAAI,CAAS;IACb,eAAe,CAAkB;IACnC,YAAY,GAAa,EAAE,CAAC;IAEpC,YAAY,IAAY,EAAE,eAAgC;QACxD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAMD,gBAAgB;IACN,KAAK,CAAC,cAAc;QAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;QAExD,yBAAyB;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;QAEzE,mBAAmB;QACnB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;YACjE,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,QAAQ,GAAG,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,GAAG,CAAC;YAErE,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,iCAAiC,QAAQ,IAAI,CAAC,CAAC;gBACzE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,iCAAiC;IACvB,KAAK,CAAC,SAAS,CACvB,SAA2B,EAC3B,aAAqB,CAAC,EACtB,YAAoB,IAAI;QAExB,IAAI,SAA4B,CAAC;QAEjC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC5B,OAAO,MAAM,SAAS,EAAE,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAC;gBAE3B,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBAED,sBAAsB;gBACtB,MAAM,KAAK,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBAC/C,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,aAAa,OAAO,GAAG,CAAC,wBAAwB,KAAK,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC7F,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,SAAS,UAAU,GAAG,CAAC,iCAAiC,SAAS,EAAE,OAAO,IAAI,eAAe,EAAE,CAAC,CAAC;IAChI,CAAC;IAED,qBAAqB;IACX,cAAc,CAAC,MAAc;QACrC,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,gCAAgC;IACtB,oBAAoB,CAAC,QAAsB;QACnD,MAAM,cAAc,GAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAChG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,sBAAsB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvG,CAAC;IACH,CAAC;IAED,4BAA4B;IAClB,kBAAkB,CAAC,MAAkB;QAC7C,MAAM,YAAY,GAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5G,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,oBAAoB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;CAKF"}