import { BaseAdapter } from "./base-adapter.js";
import { MarketData, CryptoData, HistoricalData, TimeInterval, TimePeriod } from "../types/market-data.js";
export declare class CoinGeckoAdapter extends BaseAdapter {
    private client;
    private readonly baseUrl;
    constructor();
    getName(): string;
    isAvailable(): Promise<boolean>;
    initialize(): Promise<void>;
    getMarketData(symbol: string): Promise<MarketData>;
    getCryptoData(symbol: string, vsCurrency?: string): Promise<CryptoData>;
    getHistoricalData(symbol: string, interval: TimeInterval, period: TimePeriod): Promise<HistoricalData>;
    private getCoinId;
    private coinIdCache;
    private getCachedCoinId;
    private setCachedCoinId;
    private mapPeriodToDays;
    private getDaysFromYearStart;
    getTopCryptos(limit?: number, vsCurrency?: string): Promise<CryptoData[]>;
}
//# sourceMappingURL=coingecko.d.ts.map