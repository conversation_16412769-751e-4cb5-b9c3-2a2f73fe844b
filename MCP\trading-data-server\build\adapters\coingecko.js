import axios from "axios";
import { BaseAdapter } from "./base-adapter.js";
export class CoinGeckoAdapter extends BaseAdapter {
    client;
    baseUrl = "https://api.coingecko.com/api/v3";
    constructor() {
        super("CoinGecko", {
            maxRequests: 50,
            windowMs: 60000 // 50 requests per minute for free tier
        });
        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: 10000,
            headers: {
                'Accept': 'application/json'
            }
        });
    }
    getName() {
        return "CoinGecko";
    }
    async isAvailable() {
        try {
            const response = await this.client.get('/ping', { timeout: 5000 });
            return response.status === 200;
        }
        catch (error) {
            console.warn(`[${this.name}] Availability check failed:`, error);
            return false;
        }
    }
    async initialize() {
        console.log(`[${this.name}] Initializing...`);
        const available = await this.isAvailable();
        if (!available) {
            console.warn(`[${this.name}] Service may not be available`);
        }
        else {
            console.log(`[${this.name}] Successfully initialized`);
        }
    }
    async getMarketData(symbol) {
        this.validateSymbol(symbol);
        return this.withRetry(async () => {
            // Najprv získame coin ID zo symbolu
            const coinId = await this.getCoinId(symbol);
            const response = await this.client.get('/simple/price', {
                params: {
                    ids: coinId,
                    vs_currencies: 'usd',
                    include_24hr_change: true,
                    include_24hr_vol: true,
                    include_last_updated_at: true
                }
            });
            const data = response.data[coinId];
            if (!data) {
                throw new Error(`No data found for symbol: ${symbol}`);
            }
            return {
                symbol: symbol.toUpperCase(),
                price: data.usd,
                timestamp: new Date(data.last_updated_at * 1000).toISOString(),
                volume: data.usd_24h_vol,
                change: undefined, // CoinGecko poskytuje len percentuálnu zmenu
                changePercent: data.usd_24h_change
            };
        });
    }
    async getCryptoData(symbol, vsCurrency = 'usd') {
        this.validateSymbol(symbol);
        return this.withRetry(async () => {
            const coinId = await this.getCoinId(symbol);
            // Získame detailné informácie o kryptomene
            const [priceResponse, coinResponse] = await Promise.all([
                this.client.get('/simple/price', {
                    params: {
                        ids: coinId,
                        vs_currencies: vsCurrency,
                        include_market_cap: true,
                        include_24hr_change: true,
                        include_24hr_vol: true,
                        include_last_updated_at: true
                    }
                }),
                this.client.get(`/coins/${coinId}`, {
                    params: {
                        localization: false,
                        tickers: false,
                        market_data: true,
                        community_data: false,
                        developer_data: false,
                        sparkline: false
                    }
                })
            ]);
            const priceData = priceResponse.data[coinId];
            const coinData = coinResponse.data;
            if (!priceData || !coinData) {
                throw new Error(`No data found for symbol: ${symbol}`);
            }
            return {
                symbol: symbol.toUpperCase(),
                price: priceData[vsCurrency],
                timestamp: new Date(priceData.last_updated_at * 1000).toISOString(),
                volume: priceData[`${vsCurrency}_24h_vol`],
                change: undefined,
                changePercent: priceData[`${vsCurrency}_24h_change`],
                marketCap: priceData[`${vsCurrency}_market_cap`],
                circulatingSupply: coinData.market_data?.circulating_supply,
                totalSupply: coinData.market_data?.total_supply,
                rank: coinData.market_cap_rank
            };
        });
    }
    async getHistoricalData(symbol, interval, period) {
        this.validateSymbol(symbol);
        return this.withRetry(async () => {
            const coinId = await this.getCoinId(symbol);
            const days = this.mapPeriodToDays(period);
            const response = await this.client.get(`/coins/${coinId}/ohlc`, {
                params: {
                    vs_currency: 'usd',
                    days: days
                }
            });
            const ohlcData = response.data;
            if (!ohlcData || !Array.isArray(ohlcData)) {
                throw new Error(`No historical data found for symbol: ${symbol}`);
            }
            // CoinGecko OHLC formát: [timestamp, open, high, low, close]
            const data = ohlcData.map((item) => ({
                timestamp: new Date(item[0]).toISOString(),
                open: item[1],
                high: item[2],
                low: item[3],
                close: item[4],
                volume: 0 // CoinGecko OHLC neobsahuje volume
            }));
            return {
                symbol: symbol.toUpperCase(),
                data,
                interval,
                period
            };
        });
    }
    // Získanie coin ID zo symbolu
    async getCoinId(symbol) {
        const cacheKey = `coingecko:coin_id:${symbol.toLowerCase()}`;
        // Skúsime najprv cache
        const cached = await this.getCachedCoinId(cacheKey);
        if (cached) {
            return cached;
        }
        // Ak nie je v cache, získame zo zoznamu coinov
        const response = await this.client.get('/coins/list');
        const coins = response.data;
        const coin = coins.find((c) => c.symbol.toLowerCase() === symbol.toLowerCase());
        if (!coin) {
            throw new Error(`Coin not found for symbol: ${symbol}`);
        }
        // Uložíme do cache na 24 hodín
        await this.setCachedCoinId(cacheKey, coin.id);
        return coin.id;
    }
    // Jednoduché cache pre coin IDs (môže byť nahradené globálnym cache)
    coinIdCache = new Map();
    async getCachedCoinId(key) {
        const cached = this.coinIdCache.get(key);
        if (!cached)
            return null;
        // Cache na 24 hodín
        if (Date.now() - cached.timestamp > 24 * 60 * 60 * 1000) {
            this.coinIdCache.delete(key);
            return null;
        }
        return cached.id;
    }
    async setCachedCoinId(key, id) {
        this.coinIdCache.set(key, {
            id,
            timestamp: Date.now()
        });
    }
    // Mapovanie období na dni pre CoinGecko
    mapPeriodToDays(period) {
        const mapping = {
            "1d": 1,
            "5d": 5,
            "1mo": 30,
            "3mo": 90,
            "6mo": 180,
            "1y": 365,
            "2y": 730,
            "5y": 1825,
            "10y": 3650,
            "ytd": this.getDaysFromYearStart(),
            "max": 3650 // CoinGecko limit
        };
        return mapping[period] || 30;
    }
    getDaysFromYearStart() {
        const now = new Date();
        const yearStart = new Date(now.getFullYear(), 0, 1);
        return Math.floor((now.getTime() - yearStart.getTime()) / (1000 * 60 * 60 * 24));
    }
    // Získanie top kryptomien podľa market cap
    async getTopCryptos(limit = 10, vsCurrency = 'usd') {
        return this.withRetry(async () => {
            const response = await this.client.get('/coins/markets', {
                params: {
                    vs_currency: vsCurrency,
                    order: 'market_cap_desc',
                    per_page: Math.min(limit, 250), // CoinGecko limit
                    page: 1,
                    sparkline: false,
                    price_change_percentage: '24h'
                }
            });
            return response.data.map((coin) => ({
                symbol: coin.symbol.toUpperCase(),
                price: coin.current_price,
                timestamp: new Date().toISOString(),
                volume: coin.total_volume,
                change: coin.price_change_24h,
                changePercent: coin.price_change_percentage_24h,
                marketCap: coin.market_cap,
                circulatingSupply: coin.circulating_supply,
                totalSupply: coin.total_supply,
                rank: coin.market_cap_rank
            }));
        });
    }
}
//# sourceMappingURL=coingecko.js.map