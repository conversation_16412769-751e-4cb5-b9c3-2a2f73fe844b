{"version": 3, "file": "coingecko.js", "sourceRoot": "", "sources": ["../../src/adapters/coingecko.ts"], "names": [], "mappings": "AAAA,OAAO,KAAwB,MAAM,OAAO,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAGhD,MAAM,OAAO,gBAAiB,SAAQ,WAAW;IACvC,MAAM,CAAgB;IACb,OAAO,GAAG,kCAAkC,CAAC;IAE9D;QACE,KAAK,CAAC,WAAW,EAAE;YACjB,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,KAAK,CAAC,uCAAuC;SACxD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,QAAQ,EAAE,kBAAkB;aAC7B;SACF,CAAC,CAAC;IACL,CAAC;IAED,OAAO;QACL,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACnE,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,mBAAmB,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,gCAAgC,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,4BAA4B,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc;QAChC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE5B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,oCAAoC;YACpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAE5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE;gBACtD,MAAM,EAAE;oBACN,GAAG,EAAE,MAAM;oBACX,aAAa,EAAE,KAAK;oBACpB,mBAAmB,EAAE,IAAI;oBACzB,gBAAgB,EAAE,IAAI;oBACtB,uBAAuB,EAAE,IAAI;iBAC9B;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,KAAK,EAAE,IAAI,CAAC,GAAG;gBACf,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBAC9D,MAAM,EAAE,IAAI,CAAC,WAAW;gBACxB,MAAM,EAAE,SAAS,EAAE,6CAA6C;gBAChE,aAAa,EAAE,IAAI,CAAC,cAAc;aACnC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,aAAqB,KAAK;QAC5D,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE5B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAE5C,2CAA2C;YAC3C,MAAM,CAAC,aAAa,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACtD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE;oBAC/B,MAAM,EAAE;wBACN,GAAG,EAAE,MAAM;wBACX,aAAa,EAAE,UAAU;wBACzB,kBAAkB,EAAE,IAAI;wBACxB,mBAAmB,EAAE,IAAI;wBACzB,gBAAgB,EAAE,IAAI;wBACtB,uBAAuB,EAAE,IAAI;qBAC9B;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE,EAAE;oBAClC,MAAM,EAAE;wBACN,YAAY,EAAE,KAAK;wBACnB,OAAO,EAAE,KAAK;wBACd,WAAW,EAAE,IAAI;wBACjB,cAAc,EAAE,KAAK;wBACrB,cAAc,EAAE,KAAK;wBACrB,SAAS,EAAE,KAAK;qBACjB;iBACF,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC;YAEnC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,KAAK,EAAE,SAAS,CAAC,UAAU,CAAC;gBAC5B,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBACnE,MAAM,EAAE,SAAS,CAAC,GAAG,UAAU,UAAU,CAAC;gBAC1C,MAAM,EAAE,SAAS;gBACjB,aAAa,EAAE,SAAS,CAAC,GAAG,UAAU,aAAa,CAAC;gBACpD,SAAS,EAAE,SAAS,CAAC,GAAG,UAAU,aAAa,CAAC;gBAChD,iBAAiB,EAAE,QAAQ,CAAC,WAAW,EAAE,kBAAkB;gBAC3D,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE,YAAY;gBAC/C,IAAI,EAAE,QAAQ,CAAC,eAAe;aAC/B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,QAAsB,EAAE,MAAkB;QAChF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE5B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,OAAO,EAAE;gBAC9D,MAAM,EAAE;oBACN,WAAW,EAAE,KAAK;oBAClB,IAAI,EAAE,IAAI;iBACX;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC/B,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,wCAAwC,MAAM,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,6DAA6D;YAC7D,MAAM,IAAI,GAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAc,EAAE,EAAE,CAAC,CAAC;gBAC1D,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;gBAC1C,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBACb,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBACb,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;gBACZ,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBACd,MAAM,EAAE,CAAC,CAAC,mCAAmC;aAC9C,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,IAAI;gBACJ,QAAQ;gBACR,MAAM;aACP,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,8BAA8B;IACtB,KAAK,CAAC,SAAS,CAAC,MAAc;QACpC,MAAM,QAAQ,GAAG,qBAAqB,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QAE7D,uBAAuB;QACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,+CAA+C;QAC/C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;QAE5B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,EAAE,CACjC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,WAAW,EAAE,CAChD,CAAC;QAEF,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,8BAA8B,MAAM,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,+BAA+B;QAC/B,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QAE9C,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC;IAED,qEAAqE;IAC7D,WAAW,GAAG,IAAI,GAAG,EAA6C,CAAC;IAEnE,KAAK,CAAC,eAAe,CAAC,GAAW;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAEzB,oBAAoB;QACpB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;YACxD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,MAAM,CAAC,EAAE,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,GAAW,EAAE,EAAU;QACnD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE;YACxB,EAAE;YACF,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;IAED,wCAAwC;IAChC,eAAe,CAAC,MAAkB;QACxC,MAAM,OAAO,GAA+B;YAC1C,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,GAAG;YACV,IAAI,EAAE,GAAG;YACT,IAAI,EAAE,GAAG;YACT,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAClC,KAAK,EAAE,IAAI,CAAC,kBAAkB;SAC/B,CAAC;QAEF,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAEO,oBAAoB;QAC1B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACnF,CAAC;IAED,2CAA2C;IAC3C,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,EAAE,aAAqB,KAAK;QAChE,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE;gBACvD,MAAM,EAAE;oBACN,WAAW,EAAE,UAAU;oBACvB,KAAK,EAAE,iBAAiB;oBACxB,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,kBAAkB;oBAClD,IAAI,EAAE,CAAC;oBACP,SAAS,EAAE,KAAK;oBAChB,uBAAuB,EAAE,KAAK;iBAC/B;aACF,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBACvC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBACjC,KAAK,EAAE,IAAI,CAAC,aAAa;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,IAAI,CAAC,YAAY;gBACzB,MAAM,EAAE,IAAI,CAAC,gBAAgB;gBAC7B,aAAa,EAAE,IAAI,CAAC,2BAA2B;gBAC/C,SAAS,EAAE,IAAI,CAAC,UAAU;gBAC1B,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;gBAC1C,WAAW,EAAE,IAAI,CAAC,YAAY;gBAC9B,IAAI,EAAE,IAAI,CAAC,eAAe;aAC3B,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}