import { BaseAdapter } from "./base-adapter.js";
import { MarketData, ForexData, StockData, HistoricalData, TimeInterval, TimePeriod } from "../types/market-data.js";
export declare class YahooFinanceAdapter extends BaseAdapter {
    private client;
    private readonly baseUrl;
    constructor();
    getName(): string;
    isAvailable(): Promise<boolean>;
    initialize(): Promise<void>;
    getMarketData(symbol: string): Promise<MarketData>;
    getHistoricalData(symbol: string, interval: TimeInterval, period: TimePeriod): Promise<HistoricalData>;
    getForexData(pair: string): Promise<ForexData>;
    getStockData(symbol: string): Promise<StockData>;
    private mapInterval;
    private mapPeriod;
    getMultipleMarketData(symbols: string[]): Promise<MarketData[]>;
}
//# sourceMappingURL=yahoo-finance.d.ts.map