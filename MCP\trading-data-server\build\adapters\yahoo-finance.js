import axios from "axios";
import { BaseAdapter } from "./base-adapter.js";
export class YahooFinanceAdapter extends BaseAdapter {
    client;
    baseUrl = "https://query1.finance.yahoo.com/v8/finance/chart";
    constructor() {
        super("YahooFinance", {
            maxRequests: 60,
            windowMs: 60000 // 60 requests per minute
        });
        this.client = axios.create({
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });
    }
    getName() {
        return "Yahoo Finance";
    }
    async isAvailable() {
        try {
            const response = await this.client.get(`${this.baseUrl}/AAPL`, {
                params: { interval: '1d', range: '1d' },
                timeout: 5000
            });
            return response.status === 200;
        }
        catch (error) {
            console.warn(`[${this.name}] Availability check failed:`, error);
            return false;
        }
    }
    async initialize() {
        console.log(`[${this.name}] Initializing...`);
        const available = await this.isAvailable();
        if (!available) {
            console.warn(`[${this.name}] Service may not be available`);
        }
        else {
            console.log(`[${this.name}] Successfully initialized`);
        }
    }
    async getMarketData(symbol) {
        this.validateSymbol(symbol);
        return this.withRetry(async () => {
            const response = await this.client.get(`${this.baseUrl}/${symbol}`, {
                params: {
                    interval: '1m',
                    range: '1d'
                }
            });
            const result = response.data.chart.result[0];
            if (!result) {
                throw new Error(`No data found for symbol: ${symbol}`);
            }
            const meta = result.meta;
            const quote = result.indicators.quote[0];
            const timestamps = result.timestamp;
            // Posledné dostupné dáta
            const lastIndex = timestamps.length - 1;
            const price = quote.close[lastIndex] || meta.regularMarketPrice;
            const volume = quote.volume[lastIndex];
            const previousClose = meta.previousClose;
            const change = price - previousClose;
            const changePercent = (change / previousClose) * 100;
            return {
                symbol: symbol.toUpperCase(),
                price,
                timestamp: new Date(timestamps[lastIndex] * 1000).toISOString(),
                volume,
                change,
                changePercent
            };
        });
    }
    async getHistoricalData(symbol, interval, period) {
        this.validateSymbol(symbol);
        this.validateTimeInterval(interval);
        this.validateTimePeriod(period);
        return this.withRetry(async () => {
            const response = await this.client.get(`${this.baseUrl}/${symbol}`, {
                params: {
                    interval: this.mapInterval(interval),
                    range: this.mapPeriod(period)
                }
            });
            const result = response.data.chart.result[0];
            if (!result) {
                throw new Error(`No historical data found for symbol: ${symbol}`);
            }
            const timestamps = result.timestamp;
            const quote = result.indicators.quote[0];
            const data = timestamps.map((timestamp, index) => ({
                timestamp: new Date(timestamp * 1000).toISOString(),
                open: quote.open[index] || 0,
                high: quote.high[index] || 0,
                low: quote.low[index] || 0,
                close: quote.close[index] || 0,
                volume: quote.volume[index] || 0
            })).filter((item) => item.close > 0); // Filter out invalid data
            return {
                symbol: symbol.toUpperCase(),
                data,
                interval,
                period
            };
        });
    }
    async getForexData(pair) {
        // Yahoo Finance používa formát EURUSD=X pre forex páry
        const yahooSymbol = pair.includes('=X') ? pair : `${pair}=X`;
        const marketData = await this.getMarketData(yahooSymbol);
        return {
            ...marketData,
            pair: pair.replace('=X', ''),
            // Yahoo Finance neposkytuje bid/ask pre free API
            bid: undefined,
            ask: undefined,
            spread: undefined
        };
    }
    async getStockData(symbol) {
        const marketData = await this.getMarketData(symbol);
        // Pre základné stock dáta, rozšírené informácie by vyžadovali ďalšie API volania
        return {
            ...marketData,
            marketCap: undefined,
            pe: undefined,
            dividend: undefined,
            sector: undefined
        };
    }
    // Mapovanie intervalov na Yahoo Finance formát
    mapInterval(interval) {
        const mapping = {
            "1m": "1m",
            "5m": "5m",
            "15m": "15m",
            "30m": "30m",
            "1h": "1h",
            "4h": "4h",
            "1d": "1d",
            "1w": "1wk",
            "1M": "1mo"
        };
        return mapping[interval] || "1d";
    }
    // Mapovanie období na Yahoo Finance formát
    mapPeriod(period) {
        const mapping = {
            "1d": "1d",
            "5d": "5d",
            "1mo": "1mo",
            "3mo": "3mo",
            "6mo": "6mo",
            "1y": "1y",
            "2y": "2y",
            "5y": "5y",
            "10y": "10y",
            "ytd": "ytd",
            "max": "max"
        };
        return mapping[period] || "1mo";
    }
    // Pomocná metóda pre získanie viacerých symbolov naraz
    async getMultipleMarketData(symbols) {
        const promises = symbols.map(symbol => this.getMarketData(symbol).catch(error => {
            console.warn(`Failed to get data for ${symbol}:`, error.message);
            return null;
        }));
        const results = await Promise.all(promises);
        return results.filter((result) => result !== null);
    }
}
//# sourceMappingURL=yahoo-finance.js.map