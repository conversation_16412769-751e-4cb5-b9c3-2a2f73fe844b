{"version": 3, "file": "yahoo-finance.js", "sourceRoot": "", "sources": ["../../src/adapters/yahoo-finance.ts"], "names": [], "mappings": "AAAA,OAAO,KAAwB,MAAM,OAAO,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAGhD,MAAM,OAAO,mBAAoB,SAAQ,WAAW;IAC1C,MAAM,CAAgB;IACb,OAAO,GAAG,mDAAmD,CAAC;IAE/E;QACE,KAAK,CAAC,cAAc,EAAE;YACpB,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,KAAK,CAAC,yBAAyB;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,YAAY,EAAE,qHAAqH;aACpI;SACF,CAAC,CAAC;IACL,CAAC;IAED,OAAO;QACL,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,OAAO,EAAE;gBAC7D,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;gBACvC,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,mBAAmB,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,gCAAgC,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,4BAA4B,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAc;QAChC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE5B,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,MAAM,EAAE,EAAE;gBAClE,MAAM,EAAE;oBACN,QAAQ,EAAE,IAAI;oBACd,KAAK,EAAE,IAAI;iBACZ;aACF,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YACzB,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;YAEpC,yBAAyB;YACzB,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;YACxC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC;YAChE,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;YAEzC,MAAM,MAAM,GAAG,KAAK,GAAG,aAAa,CAAC;YACrC,MAAM,aAAa,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;YAErD,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,KAAK;gBACL,SAAS,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBAC/D,MAAM;gBACN,MAAM;gBACN,aAAa;aACd,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,QAAsB,EAAE,MAAkB;QAChF,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC5B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACpC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,IAAI,MAAM,EAAE,EAAE;gBAClE,MAAM,EAAE;oBACN,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;oBACpC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;iBAC9B;aACF,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,wCAAwC,MAAM,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;YACpC,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEzC,MAAM,IAAI,GAAgB,UAAU,CAAC,GAAG,CAAC,CAAC,SAAiB,EAAE,KAAa,EAAE,EAAE,CAAC,CAAC;gBAC9E,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBACnD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC5B,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC5B,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC9B,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;aACjC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAe,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,0BAA0B;YAE3E,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,IAAI;gBACJ,QAAQ;gBACR,MAAM;aACP,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,IAAY;QAC7B,uDAAuD;QACvD,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC;QAE7D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAEzD,OAAO;YACL,GAAG,UAAU;YACb,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;YAC5B,iDAAiD;YACjD,GAAG,EAAE,SAAS;YACd,GAAG,EAAE,SAAS;YACd,MAAM,EAAE,SAAS;SAClB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEpD,iFAAiF;QACjF,OAAO;YACL,GAAG,UAAU;YACb,SAAS,EAAE,SAAS;YACpB,EAAE,EAAE,SAAS;YACb,QAAQ,EAAE,SAAS;YACnB,MAAM,EAAE,SAAS;SAClB,CAAC;IACJ,CAAC;IAED,+CAA+C;IACvC,WAAW,CAAC,QAAsB;QACxC,MAAM,OAAO,GAAiC;YAC5C,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,KAAK;SACZ,CAAC;QAEF,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IACnC,CAAC;IAED,2CAA2C;IACnC,SAAS,CAAC,MAAkB;QAClC,MAAM,OAAO,GAA+B;YAC1C,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,KAAK;SACb,CAAC;QAEF,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;IAClC,CAAC;IAED,uDAAuD;IACvD,KAAK,CAAC,qBAAqB,CAAC,OAAiB;QAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACpC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvC,OAAO,CAAC,IAAI,CAAC,0BAA0B,MAAM,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CACH,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5C,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAwB,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC;IAC3E,CAAC;CACF"}