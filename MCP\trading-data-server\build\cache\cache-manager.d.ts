export interface CacheEntry<T = any> {
    data: T;
    timestamp: number;
    ttl: number;
}
export declare class CacheManager {
    private cache;
    private readonly defaultTtl;
    constructor();
    /**
     * Získa dáta z cache
     */
    get<T>(key: string): Promise<T | null>;
    /**
     * Uloží dáta do cache
     */
    set<T>(key: string, data: T, ttl?: number): Promise<void>;
    /**
     * Vymaže konkrétny kľúč z cache
     */
    delete(key: string): Promise<boolean>;
    /**
     * Vymaže všetky kľúče zodpovedajúce patternu
     */
    invalidate(pattern: string): Promise<number>;
    /**
     * Vymaže všetky dáta z cache
     */
    clear(): Promise<void>;
    /**
     * Vr<PERSON>ti štatistiky cache
     */
    getStats(): {
        totalEntries: number;
        expiredEntries: number;
        activeEntries: number;
        approximateSize: number;
        hitRate: number;
    };
    /**
     * Generuje cache kľúč z prefixu a parametrov
     */
    generateKey(prefix: string, params: Record<string, any>): string;
    /**
     * Skontroluje, či je entry expirovaný
     */
    private isExpired;
    /**
     * Vyčistí expirované entries
     */
    private cleanup;
    private hitCount;
    private missCount;
    private getHitRate;
    private recordHit;
    private recordMiss;
    /**
     * Wrapper pre get s hit/miss tracking
     */
    getWithTracking<T>(key: string): Promise<T | null>;
    /**
     * Cache-or-fetch pattern
     */
    getOrFetch<T>(key: string, fetchFn: () => Promise<T>, ttl?: number): Promise<T>;
}
export declare const cache: CacheManager;
//# sourceMappingURL=cache-manager.d.ts.map