export class CacheManager {
    cache = new Map();
    defaultTtl = 300000; // 5 minút v ms
    constructor() {
        // Cleanup expired entries každých 5 minút
        setInterval(() => this.cleanup(), 300000);
    }
    /**
     * Získa dáta z cache
     */
    async get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        if (this.isExpired(entry)) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    /**
     * <PERSON>loží d<PERSON> do cache
     */
    async set(key, data, ttl) {
        const entry = {
            data,
            timestamp: Date.now(),
            ttl: ttl || this.defaultTtl
        };
        this.cache.set(key, entry);
    }
    /**
     * Vymaže konkrétny kľúč z cache
     */
    async delete(key) {
        return this.cache.delete(key);
    }
    /**
     * Vymaže všetky kľúče zodpovedajúce patternu
     */
    async invalidate(pattern) {
        const regex = new RegExp(pattern);
        let deletedCount = 0;
        for (const key of this.cache.keys()) {
            if (regex.test(key)) {
                this.cache.delete(key);
                deletedCount++;
            }
        }
        return deletedCount;
    }
    /**
     * Vymaže všetky dáta z cache
     */
    async clear() {
        this.cache.clear();
    }
    /**
     * Vráti štatistiky cache
     */
    getStats() {
        const now = Date.now();
        let expiredCount = 0;
        let totalSize = 0;
        for (const entry of this.cache.values()) {
            if (this.isExpired(entry)) {
                expiredCount++;
            }
            totalSize += JSON.stringify(entry.data).length;
        }
        return {
            totalEntries: this.cache.size,
            expiredEntries: expiredCount,
            activeEntries: this.cache.size - expiredCount,
            approximateSize: totalSize,
            hitRate: this.getHitRate()
        };
    }
    /**
     * Generuje cache kľúč z prefixu a parametrov
     */
    generateKey(prefix, params) {
        const sortedParams = Object.keys(params)
            .sort()
            .map(key => `${key}=${params[key]}`)
            .join('&');
        return `${prefix}:${sortedParams}`;
    }
    /**
     * Skontroluje, či je entry expirovaný
     */
    isExpired(entry) {
        return Date.now() - entry.timestamp > entry.ttl;
    }
    /**
     * Vyčistí expirované entries
     */
    cleanup() {
        const expiredKeys = [];
        for (const [key, entry] of this.cache.entries()) {
            if (this.isExpired(entry)) {
                expiredKeys.push(key);
            }
        }
        expiredKeys.forEach(key => this.cache.delete(key));
        if (expiredKeys.length > 0) {
            console.log(`[Cache] Cleaned up ${expiredKeys.length} expired entries`);
        }
    }
    // Jednoduchá implementácia hit rate (môže byť rozšírená)
    hitCount = 0;
    missCount = 0;
    getHitRate() {
        const total = this.hitCount + this.missCount;
        return total === 0 ? 0 : this.hitCount / total;
    }
    // Metódy pre tracking hit/miss (volané z get metódy)
    recordHit() {
        this.hitCount++;
    }
    recordMiss() {
        this.missCount++;
    }
    /**
     * Wrapper pre get s hit/miss tracking
     */
    async getWithTracking(key) {
        const result = await this.get(key);
        if (result !== null) {
            this.recordHit();
        }
        else {
            this.recordMiss();
        }
        return result;
    }
    /**
     * Cache-or-fetch pattern
     */
    async getOrFetch(key, fetchFn, ttl) {
        let data = await this.getWithTracking(key);
        if (data === null) {
            data = await fetchFn();
            await this.set(key, data, ttl);
        }
        return data;
    }
}
// Singleton instance
export const cache = new CacheManager();
//# sourceMappingURL=cache-manager.js.map