#!/usr/bin/env node
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio";
import { z } from "zod";
// Import adaptérov
import { YahooFinanceAdapter } from "./adapters/yahoo-finance.js";
import { CoinGeckoAdapter } from "./adapters/coingecko.js";
import { AlphaVantageAdapter } from "./adapters/alpha-vantage.js";
// Import cache managera
import { CacheManager } from "./cache/cache-manager.js";
// Import tools
import { registerForexTools } from "./tools/forex-tools.js";
import { registerStockTools } from "./tools/stock-tools.js";
import { registerCryptoTools } from "./tools/crypto-tools.js";
import { registerIndicatorTools } from "./tools/indicator-tools.js";
// Import resources
import { registerMarketResources } from "./resources/market-resources.js";
// Vytvorenie MCP servera
const server = new McpServer({
    name: "trading-data-server",
    version: "0.1.0"
});
// Inicializácia adaptérov
const yahooAdapter = new YahooFinanceAdapter();
const coinGeckoAdapter = new CoinGeckoAdapter();
const alphaVantageAdapter = new AlphaVantageAdapter();
// Inicializácia cache managera
const cacheManager = new CacheManager();
// Globálny kontext pre adaptéry
export const adapters = {
    yahoo: yahooAdapter,
    coinGecko: coinGeckoAdapter,
    alphaVantage: alphaVantageAdapter
};
export const cache = cacheManager;
// Registrácia tools
registerForexTools(server);
registerStockTools(server);
registerCryptoTools(server);
registerIndicatorTools(server);
// Registrácia resources
registerMarketResources(server);
// Základný health check tool
server.tool("health_check", {
    include_adapters: z.boolean().optional().describe("Include adapter status in response")
}, async ({ include_adapters = false }) => {
    const health = {
        status: "healthy",
        timestamp: new Date().toISOString(),
        server: "trading-data-server",
        version: "0.1.0"
    };
    if (include_adapters) {
        const adapterStatus = await Promise.allSettled([
            yahooAdapter.isAvailable(),
            coinGeckoAdapter.isAvailable(),
            alphaVantageAdapter.isAvailable()
        ]);
        health.adapters = {
            yahoo: adapterStatus[0].status === 'fulfilled' ? adapterStatus[0].value : false,
            coinGecko: adapterStatus[1].status === 'fulfilled' ? adapterStatus[1].value : false,
            alphaVantage: adapterStatus[2].status === 'fulfilled' ? adapterStatus[2].value : false
        };
    }
    return {
        content: [
            {
                type: "text",
                text: JSON.stringify(health, null, 2)
            }
        ]
    };
});
// Spustenie servera
async function main() {
    try {
        console.error('🚀 Starting Trading Data MCP Server...');
        // Inicializácia adaptérov
        console.error('📡 Initializing data adapters...');
        await Promise.all([
            yahooAdapter.initialize?.(),
            coinGeckoAdapter.initialize?.(),
            alphaVantageAdapter.initialize?.()
        ].filter(Boolean));
        // Spustenie transportu
        const transport = new StdioServerTransport();
        await server.connect(transport);
        console.error('✅ Trading Data MCP Server running on stdio');
        console.error('🔧 Available tools: health_check, get_forex_data, get_stock_data, get_crypto_data, get_market_indicators');
        console.error('📊 Available resources: market://{symbol}/current, market://{symbol}/historical');
    }
    catch (error) {
        console.error('❌ Failed to start Trading Data MCP Server:', error);
        process.exit(1);
    }
}
// Graceful shutdown
process.on('SIGINT', () => {
    console.error('🛑 Shutting down Trading Data MCP Server...');
    process.exit(0);
});
process.on('SIGTERM', () => {
    console.error('🛑 Shutting down Trading Data MCP Server...');
    process.exit(0);
});
// Spustenie servera
main().catch((error) => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map