import { ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp";
import { adapters, cache } from "../index.js";
export function registerMarketResources(server) {
    // Resource pre aktuálne trhové dáta
    server.resource("current_market_data", new ResourceTemplate("market://{symbol}/current"), async (uri, { symbol }) => {
        try {
            const cacheKey = cache.generateKey("market_current", { symbol });
            let marketData = await cache.getWithTracking(cacheKey);
            if (!marketData) {
                // Skúsime rôzne adaptéry
                if (await adapters.yahoo.isAvailable()) {
                    marketData = await adapters.yahoo.getMarketData(symbol);
                }
                else if (await adapters.coinGecko.isAvailable()) {
                    marketData = await adapters.coinGecko.getMarketData(symbol);
                }
                else if (await adapters.alphaVantage.isAvailable()) {
                    marketData = await adapters.alphaVantage.getMarketData(symbol);
                }
                else {
                    throw new Error("No market data sources available");
                }
                // Cache na 30 sekúnd pre real-time dáta
                await cache.set(cacheKey, marketData, 30000);
            }
            return {
                contents: [
                    {
                        uri: uri.href,
                        mimeType: "application/json",
                        text: JSON.stringify({
                            symbol,
                            data: marketData,
                            cached: !!marketData,
                            timestamp: new Date().toISOString(),
                            source: "trading-data-server"
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            throw new Error(`Failed to get current market data for ${symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    });
    // Resource pre historické dáta
    server.resource("historical_market_data", new ResourceTemplate("market://{symbol}/historical/{interval}/{period}"), async (uri, { symbol, interval, period }) => {
        try {
            const cacheKey = cache.generateKey("market_historical", { symbol, interval, period });
            let historicalData = await cache.getWithTracking(cacheKey);
            if (!historicalData) {
                if (await adapters.yahoo.isAvailable()) {
                    historicalData = await adapters.yahoo.getHistoricalData(symbol, interval, period);
                }
                else if (await adapters.alphaVantage.isAvailable()) {
                    historicalData = await adapters.alphaVantage.getHistoricalData(symbol, interval, period);
                }
                else {
                    throw new Error("No historical data sources available");
                }
                // Cache na 30 minút pre historické dáta
                await cache.set(cacheKey, historicalData, 1800000);
            }
            return {
                contents: [
                    {
                        uri: uri.href,
                        mimeType: "application/json",
                        text: JSON.stringify({
                            symbol,
                            interval,
                            period,
                            data: historicalData,
                            cached: !!historicalData,
                            timestamp: new Date().toISOString(),
                            source: "trading-data-server"
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            throw new Error(`Failed to get historical data for ${symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    });
    // Resource pre forex dáta
    server.resource("forex_data", new ResourceTemplate("forex://{pair}/current"), async (uri, { pair }) => {
        try {
            const cacheKey = cache.generateKey("forex_current", { pair });
            let forexData = await cache.getWithTracking(cacheKey);
            if (!forexData) {
                if (await adapters.yahoo.isAvailable()) {
                    forexData = await adapters.yahoo.getForexData(pair);
                }
                else if (await adapters.alphaVantage.isAvailable() && pair.length === 6) {
                    const fromCurrency = pair.substring(0, 3);
                    const toCurrency = pair.substring(3, 6);
                    forexData = await adapters.alphaVantage.getForexData(fromCurrency, toCurrency);
                }
                else {
                    throw new Error("No forex data sources available");
                }
                await cache.set(cacheKey, forexData, 30000);
            }
            return {
                contents: [
                    {
                        uri: uri.href,
                        mimeType: "application/json",
                        text: JSON.stringify({
                            pair,
                            data: forexData,
                            cached: !!forexData,
                            timestamp: new Date().toISOString(),
                            source: "trading-data-server"
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            throw new Error(`Failed to get forex data for ${pair}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    });
    // Resource pre krypto dáta
    server.resource("crypto_data", new ResourceTemplate("crypto://{symbol}/current/{vs_currency}"), async (uri, { symbol, vs_currency = "usd" }) => {
        try {
            const cacheKey = cache.generateKey("crypto_current", { symbol, vs_currency });
            let cryptoData = await cache.getWithTracking(cacheKey);
            if (!cryptoData) {
                if (await adapters.coinGecko.isAvailable()) {
                    cryptoData = await adapters.coinGecko.getCryptoData(symbol, vs_currency);
                }
                else {
                    throw new Error("No crypto data sources available");
                }
                await cache.set(cacheKey, cryptoData, 60000); // 1 minúta pre krypto
            }
            return {
                contents: [
                    {
                        uri: uri.href,
                        mimeType: "application/json",
                        text: JSON.stringify({
                            symbol,
                            vs_currency,
                            data: cryptoData,
                            cached: !!cryptoData,
                            timestamp: new Date().toISOString(),
                            source: "trading-data-server"
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            throw new Error(`Failed to get crypto data for ${symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    });
    // Resource pre technické indikátory
    server.resource("technical_indicators", new ResourceTemplate("indicators://{symbol}/{indicator}/{period}"), async (uri, { symbol, indicator, period }) => {
        try {
            const cacheKey = cache.generateKey("indicators_resource", { symbol, indicator, period });
            let indicatorData = await cache.getWithTracking(cacheKey);
            if (!indicatorData) {
                // Získame historické dáta pre výpočet indikátora
                let historicalData;
                if (await adapters.yahoo.isAvailable()) {
                    historicalData = await adapters.yahoo.getHistoricalData(symbol, "1d", "6mo");
                }
                else if (await adapters.alphaVantage.isAvailable()) {
                    historicalData = await adapters.alphaVantage.getHistoricalData(symbol, "1d", "6mo");
                }
                else {
                    throw new Error("No data sources available for indicator calculation");
                }
                // Jednoduchý výpočet indikátora (môže byť rozšírený)
                const closePrices = historicalData.data.map((p) => p.close);
                let calculatedIndicator;
                switch (indicator.toLowerCase()) {
                    case "sma":
                        // Jednoduchý moving average
                        const periodNum = parseInt(period) || 14;
                        const smaValues = [];
                        for (let i = periodNum - 1; i < closePrices.length; i++) {
                            const sum = closePrices.slice(i - periodNum + 1, i + 1).reduce((a, b) => a + b, 0);
                            smaValues.push(sum / periodNum);
                        }
                        calculatedIndicator = {
                            name: "Simple Moving Average",
                            period: periodNum,
                            values: smaValues,
                            current: smaValues[smaValues.length - 1]
                        };
                        break;
                    default:
                        throw new Error(`Unsupported indicator: ${indicator}`);
                }
                indicatorData = {
                    symbol,
                    indicator,
                    period,
                    calculation: calculatedIndicator,
                    dataPoints: closePrices.length
                };
                await cache.set(cacheKey, indicatorData, 900000); // 15 minút
            }
            return {
                contents: [
                    {
                        uri: uri.href,
                        mimeType: "application/json",
                        text: JSON.stringify({
                            symbol,
                            indicator,
                            period,
                            data: indicatorData,
                            cached: !!indicatorData,
                            timestamp: new Date().toISOString(),
                            source: "trading-data-server"
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            throw new Error(`Failed to calculate ${indicator} for ${symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    });
    // Resource pre market status
    server.resource("market_status", { uri: "market://status/global", list: true }, async (uri) => {
        try {
            const cacheKey = "market_status_global";
            let marketStatus = await cache.getWithTracking(cacheKey);
            if (!marketStatus) {
                const now = new Date();
                const utcHour = now.getUTCHours();
                const utcDay = now.getUTCDay();
                marketStatus = {
                    timestamp: now.toISOString(),
                    markets: {
                        forex: {
                            isOpen: !(utcDay === 0 || (utcDay === 6) || (utcDay === 5 && utcHour >= 22)),
                            nextOpen: getNextForexOpen(now),
                            nextClose: getNextForexClose(now)
                        },
                        crypto: {
                            isOpen: true, // Krypto trhy sú otvorené 24/7
                            note: "Cryptocurrency markets are open 24/7"
                        },
                        us_stocks: {
                            isOpen: isUSStockMarketOpen(now),
                            nextOpen: getNextUSStockOpen(now),
                            nextClose: getNextUSStockClose(now)
                        }
                    }
                };
                await cache.set(cacheKey, marketStatus, 300000); // 5 minút
            }
            return {
                contents: [
                    {
                        uri: uri.href,
                        mimeType: "application/json",
                        text: JSON.stringify({
                            status: marketStatus,
                            cached: !!marketStatus,
                            timestamp: new Date().toISOString(),
                            source: "trading-data-server"
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            throw new Error(`Failed to get market status: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    });
}
// Pomocné funkcie pre market status
function getNextForexOpen(now) {
    // Implementácia pre ďalšie otvorenie forex trhu
    return null;
}
function getNextForexClose(now) {
    // Implementácia pre ďalšie zatvorenie forex trhu
    return null;
}
function isUSStockMarketOpen(now) {
    // Jednoduchá implementácia - US stock market je otvorený Po-Pi 9:30-16:00 EST
    const utcHour = now.getUTCHours();
    const utcDay = now.getUTCDay();
    // Preskočíme víkendy
    if (utcDay === 0 || utcDay === 6)
        return false;
    // EST je UTC-5, takže 9:30-16:00 EST je 14:30-21:00 UTC
    return utcHour >= 14 && utcHour < 21;
}
function getNextUSStockOpen(now) {
    // Implementácia pre ďalšie otvorenie US stock market
    return null;
}
function getNextUSStockClose(now) {
    // Implementácia pre ďalšie zatvorenie US stock market
    return null;
}
//# sourceMappingURL=market-resources.js.map