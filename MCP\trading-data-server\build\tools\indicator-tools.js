import { z } from "zod";
import { adapters, cache } from "../index.js";
import { SMA, EMA, RSI, MACD, BollingerBands } from "technicalindicators";
export function registerIndicatorTools(server) {
    // Nástroj pre výpočet technických indikátorov
    server.tool("get_market_indicators", {
        symbol: z.string().describe("Symbol for indicator calculation"),
        indicators: z.array(z.enum(["sma", "ema", "rsi", "macd", "bollinger", "all"])).describe("Technical indicators to calculate"),
        period: z.number().optional().default(14).describe("Period for indicator calculation"),
        data_points: z.number().optional().default(100).describe("Number of data points to use"),
        source: z.enum(["yahoo", "alphavantage", "auto"]).optional().default("auto").describe("Data source")
    }, async ({ symbol, indicators, period, data_points, source }) => {
        try {
            const cacheKey = cache.generateKey("indicators", { symbol, indicators, period, data_points, source });
            const cachedData = await cache.getWithTracking(cacheKey);
            if (cachedData) {
                return {
                    content: [
                        {
                            type: "text",
                            text: JSON.stringify({
                                ...cachedData,
                                cached: true,
                                timestamp: new Date().toISOString()
                            }, null, 2)
                        }
                    ]
                };
            }
            // Získame historické dáta
            let usedSource = source;
            if (source === "auto") {
                if (await adapters.yahoo.isAvailable()) {
                    usedSource = "yahoo";
                }
                else if (await adapters.alphaVantage.isAvailable()) {
                    usedSource = "alphavantage";
                }
                else {
                    throw new Error("No data sources available");
                }
            }
            let historicalData;
            switch (usedSource) {
                case "yahoo":
                    historicalData = await adapters.yahoo.getHistoricalData(symbol, "1d", "6mo");
                    break;
                case "alphavantage":
                    historicalData = await adapters.alphaVantage.getHistoricalData(symbol, "1d", "6mo");
                    break;
                default:
                    throw new Error(`Unsupported source: ${usedSource}`);
            }
            if (!historicalData.data || historicalData.data.length < period) {
                throw new Error(`Insufficient data for indicator calculation. Need at least ${period} data points.`);
            }
            // Pripravíme dáta pre indikátory
            const prices = historicalData.data.slice(-data_points);
            const closePrices = prices.map(p => p.close);
            const highPrices = prices.map(p => p.high);
            const lowPrices = prices.map(p => p.low);
            const volumes = prices.map(p => p.volume);
            const results = {
                symbol,
                period,
                dataPoints: prices.length,
                source: usedSource,
                indicators: {}
            };
            // Výpočet indikátorov
            const indicatorList = indicators.includes("all") ? ["sma", "ema", "rsi", "macd", "bollinger"] : indicators;
            for (const indicator of indicatorList) {
                try {
                    switch (indicator) {
                        case "sma":
                            const smaValues = SMA.calculate({ period, values: closePrices });
                            results.indicators.sma = {
                                name: "Simple Moving Average",
                                period,
                                values: smaValues,
                                current: smaValues[smaValues.length - 1],
                                signal: getSMASignal(closePrices[closePrices.length - 1], smaValues[smaValues.length - 1])
                            };
                            break;
                        case "ema":
                            const emaValues = EMA.calculate({ period, values: closePrices });
                            results.indicators.ema = {
                                name: "Exponential Moving Average",
                                period,
                                values: emaValues,
                                current: emaValues[emaValues.length - 1],
                                signal: getEMASignal(closePrices[closePrices.length - 1], emaValues[emaValues.length - 1])
                            };
                            break;
                        case "rsi":
                            const rsiValues = RSI.calculate({ period, values: closePrices });
                            results.indicators.rsi = {
                                name: "Relative Strength Index",
                                period,
                                values: rsiValues,
                                current: rsiValues[rsiValues.length - 1],
                                signal: getRSISignal(rsiValues[rsiValues.length - 1])
                            };
                            break;
                        case "macd":
                            const macdValues = MACD.calculate({
                                values: closePrices,
                                fastPeriod: 12,
                                slowPeriod: 26,
                                signalPeriod: 9,
                                SimpleMAOscillator: false,
                                SimpleMASignal: false
                            });
                            results.indicators.macd = {
                                name: "MACD",
                                values: macdValues,
                                current: macdValues[macdValues.length - 1],
                                signal: getMACDSignal(macdValues[macdValues.length - 1])
                            };
                            break;
                        case "bollinger":
                            const bbValues = BollingerBands.calculate({
                                period: 20,
                                values: closePrices,
                                stdDev: 2
                            });
                            results.indicators.bollinger = {
                                name: "Bollinger Bands",
                                period: 20,
                                stdDev: 2,
                                values: bbValues,
                                current: bbValues[bbValues.length - 1],
                                signal: getBollingerSignal(closePrices[closePrices.length - 1], bbValues[bbValues.length - 1])
                            };
                            break;
                    }
                }
                catch (error) {
                    results.indicators[indicator] = {
                        error: `Failed to calculate ${indicator}: ${error instanceof Error ? error.message : 'Unknown error'}`
                    };
                }
            }
            // Celkový signál
            results.overallSignal = calculateOverallSignal(results.indicators);
            // Cache na 15 minút
            await cache.set(cacheKey, results, 900000);
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify({
                            ...results,
                            cached: false,
                            timestamp: new Date().toISOString()
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error calculating indicators: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }
                ],
                isError: true
            };
        }
    });
    // Nástroj pre porovnanie indikátorov viacerých symbolov
    server.tool("compare_indicators", {
        symbols: z.array(z.string()).describe("Symbols to compare"),
        indicator: z.enum(["sma", "ema", "rsi", "macd"]).describe("Indicator to compare"),
        period: z.number().optional().default(14).describe("Period for calculation")
    }, async ({ symbols, indicator, period }) => {
        try {
            const results = [];
            for (const symbol of symbols) {
                try {
                    const cacheKey = cache.generateKey("indicator_compare", { symbol, indicator, period });
                    let indicatorData = await cache.getWithTracking(cacheKey);
                    if (!indicatorData) {
                        // Získame dáta a vypočítame indikátor
                        const historicalData = await adapters.yahoo.getHistoricalData(symbol, "1d", "3mo");
                        const closePrices = historicalData.data.map((p) => p.close);
                        let value;
                        switch (indicator) {
                            case "sma":
                                const smaValues = SMA.calculate({ period, values: closePrices });
                                value = smaValues[smaValues.length - 1];
                                break;
                            case "ema":
                                const emaValues = EMA.calculate({ period, values: closePrices });
                                value = emaValues[emaValues.length - 1];
                                break;
                            case "rsi":
                                const rsiValues = RSI.calculate({ period, values: closePrices });
                                value = rsiValues[rsiValues.length - 1];
                                break;
                            case "macd":
                                const macdValues = MACD.calculate({
                                    values: closePrices,
                                    fastPeriod: 12,
                                    slowPeriod: 26,
                                    signalPeriod: 9,
                                    SimpleMAOscillator: false, // Pridané
                                    SimpleMASignal: false // Pridané
                                });
                                value = macdValues[macdValues.length - 1];
                                break;
                        }
                        indicatorData = {
                            symbol,
                            indicator,
                            period,
                            value,
                            currentPrice: closePrices[closePrices.length - 1]
                        };
                        await cache.set(cacheKey, indicatorData, 300000); // 5 minút
                    }
                    results.push(indicatorData);
                }
                catch (error) {
                    results.push({
                        symbol,
                        error: error instanceof Error ? error.message : 'Unknown error'
                    });
                }
            }
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify({
                            indicator,
                            period,
                            comparison: results,
                            timestamp: new Date().toISOString()
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error comparing indicators: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }
                ],
                isError: true
            };
        }
    });
}
// Pomocné funkcie pre signály
function getSMASignal(currentPrice, smaValue) {
    if (currentPrice > smaValue)
        return "BULLISH";
    if (currentPrice < smaValue)
        return "BEARISH";
    return "NEUTRAL";
}
function getEMASignal(currentPrice, emaValue) {
    if (currentPrice > emaValue)
        return "BULLISH";
    if (currentPrice < emaValue)
        return "BEARISH";
    return "NEUTRAL";
}
function getRSISignal(rsiValue) {
    if (rsiValue > 70)
        return "OVERBOUGHT";
    if (rsiValue < 30)
        return "OVERSOLD";
    if (rsiValue > 50)
        return "BULLISH";
    return "BEARISH";
}
function getMACDSignal(macdData) {
    if (!macdData)
        return "NEUTRAL";
    const { MACD: macdLine, signal, histogram } = macdData;
    if (macdLine > signal && histogram > 0)
        return "BULLISH";
    if (macdLine < signal && histogram < 0)
        return "BEARISH";
    return "NEUTRAL";
}
function getBollingerSignal(currentPrice, bbData) {
    if (!bbData)
        return "NEUTRAL";
    const { upper, middle, lower } = bbData;
    if (currentPrice > upper)
        return "OVERBOUGHT";
    if (currentPrice < lower)
        return "OVERSOLD";
    if (currentPrice > middle)
        return "BULLISH";
    return "BEARISH";
}
function calculateOverallSignal(indicators) {
    const signals = []; // Explicitne typované ako string[]
    Object.values(indicators).forEach((indicator) => {
        if (indicator.signal) {
            signals.push(indicator.signal);
        }
    });
    if (signals.length === 0)
        return "NEUTRAL";
    const bullishCount = signals.filter(s => s === "BULLISH").length;
    const bearishCount = signals.filter(s => s === "BEARISH").length;
    if (bullishCount > bearishCount)
        return "BULLISH";
    if (bearishCount > bullishCount)
        return "BEARISH";
    return "NEUTRAL";
}
//# sourceMappingURL=indicator-tools.js.map