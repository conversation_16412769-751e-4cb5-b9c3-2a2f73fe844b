import { z } from "zod";
import { adapters, cache } from "../index.js";
export function registerStockTools(server) {
    // Nástroj pre získanie akciových dát
    server.tool("get_stock_data", {
        symbol: z.string().describe("Stock symbol (e.g., AAPL, MSFT, GOOGL)"),
        interval: z.enum(["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1M"]).optional().default("1d").describe("Time interval"),
        period: z.enum(["1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y", "10y", "ytd", "max"]).optional().default("1mo").describe("Time period"),
        source: z.enum(["yahoo", "alphavantage", "auto"]).optional().default("auto").describe("Data source preference")
    }, async ({ symbol, interval, period, source }) => {
        try {
            const cacheKey = cache.generateKey("stock", { symbol, interval, period, source });
            // Skúsime cache najprv
            const cachedData = await cache.getWithTracking(cacheKey);
            if (cachedData) {
                return {
                    content: [
                        {
                            type: "text",
                            text: JSON.stringify({
                                ...cachedData,
                                cached: true,
                                timestamp: new Date().toISOString()
                            }, null, 2)
                        }
                    ]
                };
            }
            let result;
            let usedSource = source;
            if (source === "auto") {
                if (await adapters.yahoo.isAvailable()) {
                    usedSource = "yahoo";
                }
                else if (await adapters.alphaVantage.isAvailable()) {
                    usedSource = "alphavantage";
                }
                else {
                    throw new Error("No stock data sources available");
                }
            }
            // Získanie dát podľa zdroja
            switch (usedSource) {
                case "yahoo":
                    if (interval === "1d" && period === "1d") {
                        result = await adapters.yahoo.getStockData(symbol);
                    }
                    else {
                        const historicalData = await adapters.yahoo.getHistoricalData(symbol, interval, period);
                        result = {
                            symbol,
                            source: "yahoo",
                            interval,
                            period,
                            data: historicalData.data,
                            current: historicalData.data[historicalData.data.length - 1],
                            summary: {
                                totalBars: historicalData.data.length,
                                firstDate: historicalData.data[0]?.timestamp,
                                lastDate: historicalData.data[historicalData.data.length - 1]?.timestamp
                            }
                        };
                    }
                    break;
                case "alphavantage":
                    if (interval === "1d" && period === "1d") {
                        result = await adapters.alphaVantage.getStockData(symbol);
                    }
                    else {
                        const historicalData = await adapters.alphaVantage.getHistoricalData(symbol, interval, period);
                        result = {
                            symbol,
                            source: "alphavantage",
                            interval,
                            period,
                            data: historicalData.data,
                            current: historicalData.data[historicalData.data.length - 1],
                            summary: {
                                totalBars: historicalData.data.length,
                                firstDate: historicalData.data[0]?.timestamp,
                                lastDate: historicalData.data[historicalData.data.length - 1]?.timestamp
                            }
                        };
                    }
                    break;
                default:
                    throw new Error(`Unsupported stock data source: ${usedSource}`);
            }
            // Uložíme do cache
            const ttl = interval === "1m" ? 30000 : interval === "5m" ? 300000 : 1800000;
            await cache.set(cacheKey, result, ttl);
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify({
                            ...result,
                            cached: false,
                            source: usedSource,
                            timestamp: new Date().toISOString()
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching stock data: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }
                ],
                isError: true
            };
        }
    });
    // Nástroj pre získanie viacerých akcií naraz
    server.tool("get_multiple_stocks", {
        symbols: z.array(z.string()).describe("Array of stock symbols (e.g., ['AAPL', 'MSFT', 'GOOGL'])"),
        source: z.enum(["yahoo", "alphavantage", "auto"]).optional().default("auto").describe("Data source preference")
    }, async ({ symbols, source }) => {
        try {
            const results = [];
            let usedSource = source;
            if (source === "auto") {
                if (await adapters.yahoo.isAvailable()) {
                    usedSource = "yahoo";
                }
                else if (await adapters.alphaVantage.isAvailable()) {
                    usedSource = "alphavantage";
                }
                else {
                    throw new Error("No stock data sources available");
                }
            }
            for (const symbol of symbols) {
                try {
                    const cacheKey = cache.generateKey("stock_current", { symbol, source: usedSource });
                    let stockData = await cache.getWithTracking(cacheKey);
                    if (!stockData) {
                        switch (usedSource) {
                            case "yahoo":
                                stockData = await adapters.yahoo.getStockData(symbol);
                                break;
                            case "alphavantage":
                                stockData = await adapters.alphaVantage.getStockData(symbol);
                                break;
                        }
                        await cache.set(cacheKey, stockData, 30000);
                    }
                    results.push({
                        symbol,
                        ...(stockData && typeof stockData === 'object' ? stockData : {}), // Pridaná kontrola
                        cached: !!stockData
                    });
                }
                catch (error) {
                    results.push({
                        symbol,
                        error: error instanceof Error ? error.message : 'Unknown error'
                    });
                }
            }
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify({
                            source: usedSource,
                            timestamp: new Date().toISOString(),
                            results
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error fetching multiple stock data: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }
                ],
                isError: true
            };
        }
    });
    // Nástroj pre vyhľadávanie akcií
    server.tool("search_stocks", {
        query: z.string().describe("Search query (company name or symbol)"),
        limit: z.number().optional().default(10).describe("Maximum number of results")
    }, async ({ query, limit }) => {
        try {
            // Jednoduchá implementácia - môže byť rozšírená o skutočné vyhľadávanie
            const popularStocks = [
                { symbol: "AAPL", name: "Apple Inc." },
                { symbol: "MSFT", name: "Microsoft Corporation" },
                { symbol: "GOOGL", name: "Alphabet Inc." },
                { symbol: "AMZN", name: "Amazon.com Inc." },
                { symbol: "TSLA", name: "Tesla Inc." },
                { symbol: "META", name: "Meta Platforms Inc." },
                { symbol: "NVDA", name: "NVIDIA Corporation" },
                { symbol: "NFLX", name: "Netflix Inc." },
                { symbol: "AMD", name: "Advanced Micro Devices" },
                { symbol: "INTC", name: "Intel Corporation" }
            ];
            const filtered = popularStocks.filter(stock => stock.symbol.toLowerCase().includes(query.toLowerCase()) ||
                stock.name.toLowerCase().includes(query.toLowerCase())).slice(0, limit);
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify({
                            query,
                            results: filtered,
                            timestamp: new Date().toISOString()
                        }, null, 2)
                    }
                ]
            };
        }
        catch (error) {
            return {
                content: [
                    {
                        type: "text",
                        text: `Error searching stocks: ${error instanceof Error ? error.message : 'Unknown error'}`
                    }
                ],
                isError: true
            };
        }
    });
}
//# sourceMappingURL=stock-tools.js.map