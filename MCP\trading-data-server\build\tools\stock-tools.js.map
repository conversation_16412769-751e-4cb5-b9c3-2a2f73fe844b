{"version": 3, "file": "stock-tools.js", "sourceRoot": "", "sources": ["../../src/tools/stock-tools.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAE9C,MAAM,UAAU,kBAAkB,CAAC,MAAiB;IAElD,qCAAqC;IACrC,MAAM,CAAC,IAAI,CACT,gBAAgB,EAChB;QACE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;QACrE,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC;QAC7H,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC;QAC1I,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC;KAChH,EACD,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAO,EAAE,EAAE;QAClD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YAElF,uBAAuB;YACvB,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACzD,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO;oBACL,OAAO,EAAE;wBACP;4BACE,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,GAAG,UAAU;gCACb,MAAM,EAAE,IAAI;gCACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;6BACpC,EAAE,IAAI,EAAE,CAAC,CAAC;yBACZ;qBACF;iBACF,CAAC;YACJ,CAAC;YAED,IAAI,MAAM,CAAC;YACX,IAAI,UAAU,GAAG,MAAM,CAAC;YAExB,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBACtB,IAAI,MAAM,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;oBACvC,UAAU,GAAG,OAAO,CAAC;gBACvB,CAAC;qBAAM,IAAI,MAAM,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;oBACrD,UAAU,GAAG,cAAc,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,OAAO;oBACV,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;wBACzC,MAAM,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;oBACrD,CAAC;yBAAM,CAAC;wBACN,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;wBACxF,MAAM,GAAG;4BACP,MAAM;4BACN,MAAM,EAAE,OAAO;4BACf,QAAQ;4BACR,MAAM;4BACN,IAAI,EAAE,cAAc,CAAC,IAAI;4BACzB,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;4BAC5D,OAAO,EAAE;gCACP,SAAS,EAAE,cAAc,CAAC,IAAI,CAAC,MAAM;gCACrC,SAAS,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS;gCAC5C,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,SAAS;6BACzE;yBACF,CAAC;oBACJ,CAAC;oBACD,MAAM;gBAER,KAAK,cAAc;oBACjB,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;wBACzC,MAAM,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;oBAC5D,CAAC;yBAAM,CAAC;wBACN,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;wBAC/F,MAAM,GAAG;4BACP,MAAM;4BACN,MAAM,EAAE,cAAc;4BACtB,QAAQ;4BACR,MAAM;4BACN,IAAI,EAAE,cAAc,CAAC,IAAI;4BACzB,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;4BAC5D,OAAO,EAAE;gCACP,SAAS,EAAE,cAAc,CAAC,IAAI,CAAC,MAAM;gCACrC,SAAS,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS;gCAC5C,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,SAAS;6BACzE;yBACF,CAAC;oBACJ,CAAC;oBACD,MAAM;gBAER;oBACE,MAAM,IAAI,KAAK,CAAC,kCAAkC,UAAU,EAAE,CAAC,CAAC;YACpE,CAAC;YAED,mBAAmB;YACnB,MAAM,GAAG,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;YAC7E,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YAEvC,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,GAAG,MAAM;4BACT,MAAM,EAAE,KAAK;4BACb,MAAM,EAAE,UAAU;4BAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ;iBACF;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAC/F;iBACF;gBACD,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;IAEF,6CAA6C;IAC7C,MAAM,CAAC,IAAI,CACT,qBAAqB,EACrB;QACE,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,0DAA0D,CAAC;QACjG,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC;KAChH,EACD,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAO,EAAE,EAAE;QACjC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,IAAI,UAAU,GAAG,MAAM,CAAC;YAExB,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBACtB,IAAI,MAAM,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;oBACvC,UAAU,GAAG,OAAO,CAAC;gBACvB,CAAC;qBAAM,IAAI,MAAM,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;oBACrD,UAAU,GAAG,cAAc,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;YAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;oBAEpF,IAAI,SAAS,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAEtD,IAAI,CAAC,SAAS,EAAE,CAAC;wBACf,QAAQ,UAAU,EAAE,CAAC;4BACnB,KAAK,OAAO;gCACV,SAAS,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gCACtD,MAAM;4BACR,KAAK,cAAc;gCACjB,SAAS,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gCAC7D,MAAM;wBACV,CAAC;wBAED,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;oBAC9C,CAAC;oBAED,OAAO,CAAC,IAAI,CAAC;wBACX,MAAM;wBACN,GAAG,CAAC,SAAS,IAAI,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,mBAAmB;wBACrF,MAAM,EAAE,CAAC,CAAC,SAAS;qBACpB,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC;wBACX,MAAM;wBACN,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;qBAChE,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,MAAM,EAAE,UAAU;4BAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;4BACnC,OAAO;yBACR,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ;iBACF;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,uCAAuC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBACxG;iBACF;gBACD,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;IAEF,iCAAiC;IACjC,MAAM,CAAC,IAAI,CACT,eAAe,EACf;QACE,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QACnE,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,2BAA2B,CAAC;KAC/E,EACD,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAO,EAAE,EAAE;QAC9B,IAAI,CAAC;YACH,wEAAwE;YACxE,MAAM,aAAa,GAAG;gBACpB,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE;gBACtC,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,uBAAuB,EAAE;gBACjD,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE;gBAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE;gBAC3C,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE;gBACtC,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,qBAAqB,EAAE;gBAC/C,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,oBAAoB,EAAE;gBAC9C,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE;gBACxC,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,wBAAwB,EAAE;gBACjD,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,mBAAmB,EAAE;aAC9C,CAAC;YAEF,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC5C,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACxD,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CACvD,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAElB,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,KAAK;4BACL,OAAO,EAAE,QAAQ;4BACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ;iBACF;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,2BAA2B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;qBAC5F;iBACF;gBACD,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;IACH,CAAC,CACF,CAAC;AACJ,CAAC"}