export interface MarketData {
    symbol: string;
    price: number;
    timestamp: string;
    volume?: number;
    change?: number;
    changePercent?: number;
}
export interface OHLCVData {
    timestamp: string;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
}
export interface ForexData extends MarketData {
    pair: string;
    bid?: number;
    ask?: number;
    spread?: number;
}
export interface StockData extends MarketData {
    marketCap?: number;
    pe?: number;
    dividend?: number;
    sector?: string;
}
export interface CryptoData extends MarketData {
    marketCap?: number;
    circulatingSupply?: number;
    totalSupply?: number;
    rank?: number;
}
export interface HistoricalData {
    symbol: string;
    data: OHLCVData[];
    interval: string;
    period: string;
}
export interface TechnicalIndicator {
    name: string;
    symbol: string;
    values: number[];
    timestamps: string[];
    parameters: Record<string, any>;
}
export type TimeInterval = "1m" | "5m" | "15m" | "30m" | "1h" | "4h" | "1d" | "1w" | "1M";
export type TimePeriod = "1d" | "5d" | "1mo" | "3mo" | "6mo" | "1y" | "2y" | "5y" | "10y" | "ytd" | "max";
//# sourceMappingURL=market-data.d.ts.map