# Python Trading Agent Environment Variables
# Copy this file to .env and fill in your actual values

# Trading Configuration
TRADING_INITIAL_BALANCE=10000.0
TRADING_MAX_DAILY_TRADES=10
TRADING_MIN_CONFIDENCE=70.0
TRADING_MIN_SIGNAL_STRENGTH=60.0

# Risk Management
TRADING_MAX_RISK_PER_TRADE=0.02
TRADING_STOP_LOSS_PERCENTAGE=0.015
TRADING_TAKE_PROFIT_PERCENTAGE=0.03

# API Configuration
TRADING_API_URL=https://api.exchangerate.host/timeseries
TRADING_API_SYMBOL=EUR
TRADING_ACCESS_KEY=

# Alpha Vantage API (optional - for advanced features)
TRADING_ALPHA_VANTAGE_API_KEY=
TRADING_FOREX_API_KEY=
TRADING_TRADING_API_BASE=https://www.alphavantage.co/query

# MCP Server Configuration
TRADING_MCP_SERVER_PORT=3000
TRADING_MCP_SERVER_HOST=localhost

# Trading Pairs
TRADING_DEFAULT_CURRENCY_PAIR=EUR/USD
TRADING_DEFAULT_TIMEFRAME=5min

# Strategy Configuration
TRADING_EVOLUTION_POPULATION_SIZE=20
TRADING_EVOLUTION_GENERATIONS=10
TRADING_EVOLUTION_MUTATION_RATE=0.1

# Logging
TRADING_LOG_LEVEL=INFO
TRADING_LOG_FILE=trading_agent.log
TRADING_LOG_MAX_SIZE=10 MB
TRADING_LOG_RETENTION=7 days

# Database
TRADING_DATABASE_URL=sqlite:///trading_agent.db
TRADING_REDIS_URL=

# Development
TRADING_DEBUG=false
TRADING_TESTING=false
