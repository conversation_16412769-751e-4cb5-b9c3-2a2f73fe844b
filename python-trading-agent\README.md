# Python Trading Agent 2.0

🚀 **Modern Python Trading Bot with MCP Support**

A high-performance, async-first trading bot built with modern Python libraries, featuring Model Context Protocol (MCP) integration for AI-powered trading decisions.

## ✨ Features

- **🔥 Modern Python Architecture**: Built with Python 3.11+, async/await, Pydantic v2, FastAPI
- **📊 Advanced Trading Strategies**: Smart and Optimized strategies with ICT concepts
- **🤖 MCP Integration**: Model Context Protocol server for AI integration
- **⚡ High Performance**: Async data fetching, concurrent trade execution
- **🛡️ Risk Management**: Advanced position sizing, stop-loss, take-profit
- **📈 Multiple Data Sources**: Yahoo Finance, Alpha Vantage, Exchange Rate Host
- **🧪 Comprehensive Testing**: Unit tests with pytest and async support
- **📱 Rich CLI Interface**: Beautiful command-line interface with Rich
- **📝 Structured Logging**: Advanced logging with Loguru and structured output
- **⚙️ Type Safety**: Full type hints with Pydantic models

## 🏗️ Architecture

```
python-trading-agent/
├── src/trading_agent/
│   ├── core/           # Core trading engine
│   ├── strategies/     # Trading strategies
│   ├── data/          # Data fetching and processing
│   ├── mcp/           # MCP server implementation
│   ├── models/        # Pydantic data models
│   └── utils/         # Configuration, logging, utilities
├── tests/             # Comprehensive test suite
└── docs/             # Documentation
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd python-trading-agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Or install in development mode
pip install -e .
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys (optional for basic functionality)
nano .env
```

### 3. Basic Usage

```bash
# Test the system
trading-bot test

# Get current price
trading-bot price -s EUR/USD

# Analyze market with smart strategy
trading-bot analyze -s EUR/USD -st smart

# Analyze with optimized strategy
trading-bot analyze -s EUR/USD -st optimized -d 3

# Execute a trade
trading-bot trade -s EUR/USD -a buy -am 100

# View portfolio
trading-bot portfolio

# Start MCP server
trading-bot server
```

## 📊 Trading Strategies

### Smart Strategy
- **Multi-indicator analysis**: RSI, MACD, Bollinger Bands, Stochastic
- **Risk management**: Dynamic position sizing, volatility adjustment
- **Session awareness**: London, New York, Asian sessions
- **Quality grading**: A+ to F signal quality system

### Optimized Strategy
- **ICT Concepts**: Order blocks, Fair Value Gaps, Liquidity sweeps
- **Multi-timeframe analysis**: Short, medium, long-term alignment
- **Advanced timing**: Entry timing optimization
- **Enhanced risk assessment**: Dynamic risk level adjustment

## 🔌 MCP Server

The trading agent includes a Model Context Protocol server for AI integration:

```bash
# Start MCP server
trading-bot server
```

### Available MCP Tools:
- `get_market_data`: Fetch real-time market data
- `analyze_strategy`: Run strategy analysis
- `execute_trade`: Execute trades
- `get_portfolio`: Get portfolio status
- `health_check`: System health check

### Available MCP Resources:
- `trading://portfolio`: Current portfolio state
- `trading://config`: Trading configuration

## 📈 Data Sources

1. **Yahoo Finance** (Primary)
   - Real-time forex and stock data
   - Historical data with multiple timeframes
   - No API key required

2. **Alpha Vantage** (Premium)
   - Professional forex data
   - Technical indicators
   - Requires API key (free tier: 500 calls/day)

3. **Exchange Rate Host** (Backup)
   - Free forex rates
   - Daily data
   - No API key required

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src/trading_agent

# Run specific test file
pytest tests/test_trader.py

# Run async tests
pytest -v tests/test_trader.py::TestTrader::test_successful_buy_trade
```

## 📊 Performance Monitoring

The system includes comprehensive performance tracking:

- **Portfolio Metrics**: Balance, P&L, win rate, profit factor
- **Risk Metrics**: Max drawdown, consecutive losses
- **Trading Metrics**: Daily trades, position sizes
- **Strategy Metrics**: Signal confidence, quality grades

## 🛠️ Development

### Code Quality Tools

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Type checking
mypy src/

# Linting
flake8 src/ tests/
```

### Pre-commit Hooks

```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run manually
pre-commit run --all-files
```

## 📚 API Reference

### Core Classes

- **`Trader`**: Main trading engine with async execution
- **`DataFetcher`**: Multi-source data fetching with caching
- **`SmartStrategy`**: Advanced technical analysis strategy
- **`OptimizedStrategy`**: ICT-based optimized strategy
- **`TradingMCPServer`**: MCP protocol server

### Data Models

- **`TradeOrder`**: Trade order specification
- **`TradingAnalysis`**: Complete market analysis
- **`Portfolio`**: Portfolio state and metrics
- **`MarketData`**: OHLCV market data point

## 🔧 Configuration

Key configuration options in `.env`:

```bash
# Trading
TRADING_INITIAL_BALANCE=10000.0
TRADING_MAX_DAILY_TRADES=10
TRADING_MIN_CONFIDENCE=70.0

# Risk Management
TRADING_MAX_RISK_PER_TRADE=0.02
TRADING_STOP_LOSS_PERCENTAGE=0.015

# API Keys (optional)
TRADING_ALPHA_VANTAGE_API_KEY=your_key_here
```

## 🚨 Risk Disclaimer

**This software is for educational and research purposes only. Trading financial instruments involves substantial risk of loss. Past performance is not indicative of future results. Never trade with money you cannot afford to lose.**

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📞 Support

- **Issues**: GitHub Issues
- **Documentation**: `/docs` directory
- **Examples**: `/examples` directory

---

**Built with ❤️ using modern Python technologies**

- Python 3.11+
- Pydantic v2
- FastAPI
- Loguru
- Rich
- Pandas
- TA-Lib
- Pytest
