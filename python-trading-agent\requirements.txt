# Core Python Trading Bot Dependencies - 2024 Latest Versions

# Data Processing & Analysis
pandas>=2.2.0
numpy>=1.26.0
polars>=0.20.0  # Modern alternative to pandas for performance

# Technical Analysis
# TA-Lib>=0.4.28  # Requires manual installation on Windows
# pandas-ta>=0.3.14b

# HTTP & API
httpx>=0.27.0  # Modern async HTTP client
aiohttp>=3.9.0
requests>=2.31.0

# Data Validation & Models
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Web Framework (for API/Dashboard)
fastapi>=0.108.0
uvicorn>=0.25.0

# MCP (Model Context Protocol) - will install manually if needed
# mcp>=1.0.0

# Database & Caching
redis>=5.0.0
sqlite3  # Built-in
sqlalchemy>=2.0.0

# Configuration & Environment
python-dotenv>=1.0.0
pyyaml>=6.0.1
toml>=0.10.2

# Logging & Monitoring
loguru>=0.7.2
structlog>=23.2.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.23.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0

# Type Checking
mypy>=1.8.0
types-requests>=2.31.0

# Code Quality
black>=23.12.0
isort>=5.13.0
flake8>=7.0.0
pre-commit>=3.6.0

# Scientific Computing
scipy>=1.12.0
scikit-learn>=1.4.0

# Plotting & Visualization
matplotlib>=3.8.0
plotly>=5.17.0
seaborn>=0.13.0

# Utilities
click>=8.1.7  # CLI interface
rich>=13.7.0  # Rich terminal output
tqdm>=4.66.0  # Progress bars

# Forex/Trading Specific
yfinance>=0.2.18
# alpha-vantage>=2.3.1  # Will install if needed
# ccxt>=4.2.0  # Cryptocurrency exchange library

# Development Tools
ipython>=8.18.0
jupyter>=1.0.0
