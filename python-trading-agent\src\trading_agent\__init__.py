"""
Modern Python Trading Agent
==========================

A high-performance trading bot with MCP (Model Context Protocol) support,
built with modern Python libraries and async architecture.

Features:
- Async/await architecture for high performance
- MCP server for AI integration
- Multiple trading strategies
- Real-time data processing
- Risk management
- Backtesting capabilities
- Web dashboard with FastAPI
"""

__version__ = "2.0.0"
__author__ = "Trading Bot Team"
__email__ = "<EMAIL>"

from .core.trader import Trader
from .strategies.smart_strategy import SmartStrategy
from .strategies.optimized_strategy import OptimizedStrategy
from .data.fetcher import DataFetcher
from .utils.config import Config
from .utils.logger import get_logger

__all__ = [
    "Trader",
    "SmartStrategy", 
    "OptimizedStrategy",
    "DataFetcher",
    "Config",
    "get_logger",
]
