#!/usr/bin/env python3
"""
Trading Agent CLI
================

Command-line interface for the Python trading agent.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

from .core.trader import Trader
from .data.fetcher import DataFetcher
from .strategies.smart_strategy import SmartStrategy
from .strategies.optimized_strategy import OptimizedStrategy
from .mcp.server import TradingMCPServer
from .utils.config import config
from .utils.logger import get_logger

console = Console()
logger = get_logger(__name__)


@click.group()
@click.version_option(version="2.0.0")
def cli():
    """Python Trading Agent - Modern forex trading bot with MCP support."""
    pass


@cli.command()
@click.option("--symbol", "-s", default="EUR/USD", help="Trading symbol")
@click.option("--days", "-d", default=1, help="Days of data to analyze")
@click.option("--strategy", "-st", default="smart", type=click.Choice(["smart", "optimized"]), help="Strategy to use")
def analyze(symbol: str, days: int, strategy: str):
    """Analyze market data and generate trading signals."""
    console.print(f"[bold blue]Analyzing {symbol} with {strategy} strategy...[/bold blue]")
    
    async def run_analysis():
        try:
            # Initialize components
            data_fetcher = DataFetcher()
            
            if strategy == "optimized":
                strategy_instance = OptimizedStrategy()
            else:
                strategy_instance = SmartStrategy()
            
            # Fetch market data
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Fetching market data...", total=None)
                
                async with data_fetcher:
                    from datetime import datetime, timedelta
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=days)
                    
                    market_data = await data_fetcher.fetch_market_data(
                        symbol=symbol,
                        start_date=start_date.strftime("%Y-%m-%d"),
                        end_date=end_date.strftime("%Y-%m-%d"),
                        interval="5min"
                    )
                
                progress.update(task, description="Analyzing data...")
                
                # Perform analysis
                analysis = await strategy_instance.analyze(market_data)
                
                progress.update(task, description="Generating recommendation...")
                
                # Generate trade decision
                trade_order = await strategy_instance.decide_action(analysis)
            
            # Display results
            _display_analysis_results(symbol, analysis, trade_order, len(market_data))
            
        except Exception as e:
            console.print(f"[bold red]Error during analysis: {e}[/bold red]")
            logger.error(f"Analysis error: {e}")
    
    asyncio.run(run_analysis())


@cli.command()
@click.option("--symbol", "-s", default="EUR/USD", help="Trading symbol")
@click.option("--action", "-a", type=click.Choice(["buy", "sell"]), required=True, help="Trade action")
@click.option("--amount", "-am", type=float, required=True, help="Trade amount")
@click.option("--price", "-p", type=float, help="Trade price (current price if not specified)")
def trade(symbol: str, action: str, amount: float, price: Optional[float]):
    """Execute a trade order."""
    console.print(f"[bold yellow]Executing {action.upper()} order for {amount} {symbol}...[/bold yellow]")
    
    async def execute_trade():
        try:
            # Initialize trader
            trader = Trader()
            
            # Get current price if not provided
            if price is None:
                data_fetcher = DataFetcher()
                async with data_fetcher:
                    current_price = await data_fetcher.get_current_price(symbol)
                    if current_price is None:
                        console.print("[bold red]Could not get current price[/bold red]")
                        return
                    price = current_price
            
            # Create trade order
            from .models.types import TradeOrder, TradeAction
            trade_order = TradeOrder(
                action=TradeAction(action),
                symbol=symbol,
                amount=amount,
                price=price
            )
            
            # Execute trade
            success = await trader.execute_trade(trade_order)
            
            if success:
                console.print(f"[bold green]✓ Trade executed successfully![/bold green]")
                
                # Display portfolio
                portfolio = trader.get_portfolio()
                _display_portfolio(portfolio)
            else:
                console.print("[bold red]✗ Trade execution failed[/bold red]")
                
        except Exception as e:
            console.print(f"[bold red]Error executing trade: {e}[/bold red]")
            logger.error(f"Trade execution error: {e}")
    
    asyncio.run(execute_trade())


@cli.command()
def portfolio():
    """Display current portfolio status."""
    console.print("[bold blue]Portfolio Status[/bold blue]")
    
    trader = Trader()
    portfolio = trader.get_portfolio()
    performance = trader.get_performance_stats()
    
    _display_portfolio(portfolio)
    _display_performance(performance)


@cli.command()
@click.option("--symbol", "-s", default="EUR/USD", help="Trading symbol")
def price(symbol: str):
    """Get current price for a symbol."""
    console.print(f"[bold blue]Getting current price for {symbol}...[/bold blue]")
    
    async def get_price():
        try:
            data_fetcher = DataFetcher()
            async with data_fetcher:
                current_price = await data_fetcher.get_current_price(symbol)
                
                if current_price:
                    console.print(f"[bold green]{symbol}: ${current_price:.4f}[/bold green]")
                else:
                    console.print(f"[bold red]Could not get price for {symbol}[/bold red]")
                    
        except Exception as e:
            console.print(f"[bold red]Error getting price: {e}[/bold red]")
            logger.error(f"Price fetch error: {e}")
    
    asyncio.run(get_price())


@cli.command()
def server():
    """Start the MCP trading server."""
    console.print("[bold green]Starting MCP Trading Server...[/bold green]")
    
    try:
        server = TradingMCPServer()
        asyncio.run(server.run())
    except KeyboardInterrupt:
        console.print("\n[bold yellow]Server stopped by user[/bold yellow]")
    except Exception as e:
        console.print(f"[bold red]Server error: {e}[/bold red]")
        logger.error(f"Server error: {e}")


@cli.command()
def config():
    """Display current configuration."""
    console.print("[bold blue]Trading Agent Configuration[/bold blue]")
    
    config_table = Table(title="Configuration Settings")
    config_table.add_column("Setting", style="cyan")
    config_table.add_column("Value", style="green")
    
    config_table.add_row("Initial Balance", f"${config.initial_balance:,.2f}")
    config_table.add_row("Max Daily Trades", str(config.max_daily_trades))
    config_table.add_row("Min Confidence", f"{config.min_confidence}%")
    config_table.add_row("Min Signal Strength", f"{config.min_signal_strength}%")
    config_table.add_row("Default Pair", config.default_currency_pair)
    config_table.add_row("Default Timeframe", config.default_timeframe)
    config_table.add_row("Max Risk Per Trade", f"{config.max_risk_per_trade * 100}%")
    config_table.add_row("Stop Loss", f"{config.stop_loss_percentage * 100}%")
    config_table.add_row("Take Profit", f"{config.take_profit_percentage * 100}%")
    
    console.print(config_table)


@cli.command()
def test():
    """Run system tests."""
    console.print("[bold blue]Running system tests...[/bold blue]")
    
    async def run_tests():
        try:
            # Test data fetcher
            console.print("Testing data fetcher...")
            data_fetcher = DataFetcher()
            async with data_fetcher:
                price = await data_fetcher.get_current_price("EUR/USD")
                if price:
                    console.print(f"✓ Data fetcher working (EUR/USD: ${price:.4f})")
                else:
                    console.print("✗ Data fetcher failed")
            
            # Test strategies
            console.print("Testing strategies...")
            smart_strategy = SmartStrategy()
            optimized_strategy = OptimizedStrategy()
            console.print("✓ Strategies initialized")
            
            # Test trader
            console.print("Testing trader...")
            trader = Trader()
            portfolio = trader.get_portfolio()
            console.print(f"✓ Trader working (Balance: ${portfolio.balance:,.2f})")
            
            console.print("[bold green]All tests passed![/bold green]")
            
        except Exception as e:
            console.print(f"[bold red]Test failed: {e}[/bold red]")
            logger.error(f"Test error: {e}")
    
    asyncio.run(run_tests())


def _display_analysis_results(symbol: str, analysis, trade_order, data_points: int):
    """Display analysis results in a formatted table."""
    
    # Analysis summary panel
    summary_text = f"""
[bold]Symbol:[/bold] {symbol}
[bold]Data Points:[/bold] {data_points}
[bold]Recommendation:[/bold] {analysis.recommendation.value.upper()}
[bold]Confidence:[/bold] {analysis.signals.confidence:.1f}%
[bold]Signal Quality:[/bold] {analysis.signals.quality.value}
[bold]Risk Level:[/bold] {analysis.risk_level.value.upper()}
"""
    
    console.print(Panel(summary_text, title="Analysis Summary", border_style="blue"))
    
    # Signals table
    signals_table = Table(title="Signal Analysis")
    signals_table.add_column("Metric", style="cyan")
    signals_table.add_column("Value", style="green")
    
    signals_table.add_row("Buy Signal", f"{analysis.signals.buy:.1f}%")
    signals_table.add_row("Sell Signal", f"{analysis.signals.sell:.1f}%")
    signals_table.add_row("Confirmations", str(analysis.signals.confirmations))
    signals_table.add_row("Divergences", str(analysis.signals.divergences))
    
    console.print(signals_table)
    
    # Market condition table
    market_table = Table(title="Market Condition")
    market_table.add_column("Aspect", style="cyan")
    market_table.add_column("Value", style="green")
    
    market_table.add_row("Trend", analysis.market_condition.trend.upper())
    market_table.add_row("Trend Strength", f"{analysis.market_condition.trend_strength:.1f}%")
    market_table.add_row("Volatility", f"{analysis.market_condition.volatility:.1f}%")
    market_table.add_row("Volume Profile", analysis.market_condition.volume_profile.upper())
    market_table.add_row("Session", analysis.market_condition.session.value.upper())
    
    console.print(market_table)
    
    # Trade decision
    if trade_order:
        trade_text = f"""
[bold]Action:[/bold] {trade_order.action.value.upper()}
[bold]Amount:[/bold] {trade_order.amount}
[bold]Price:[/bold] ${trade_order.price:.4f}
[bold]Stop Loss:[/bold] ${trade_order.stop_loss:.4f}
[bold]Take Profit:[/bold] ${trade_order.take_profit:.4f}
"""
        console.print(Panel(trade_text, title="Trade Decision", border_style="green"))
    else:
        console.print(Panel("[bold yellow]NO TRADE[/bold yellow]", title="Trade Decision", border_style="yellow"))
    
    # Reasoning
    console.print(Panel(analysis.reasoning, title="Reasoning", border_style="magenta"))


def _display_portfolio(portfolio):
    """Display portfolio information."""
    portfolio_table = Table(title="Portfolio")
    portfolio_table.add_column("Metric", style="cyan")
    portfolio_table.add_column("Value", style="green")
    
    portfolio_table.add_row("Balance", f"${portfolio.balance:,.2f}")
    portfolio_table.add_row("Total Trades", str(portfolio.total_trades))
    portfolio_table.add_row("Winning Trades", str(portfolio.winning_trades))
    portfolio_table.add_row("Losing Trades", str(portfolio.losing_trades))
    portfolio_table.add_row("Win Rate", f"{portfolio.win_rate:.1f}%")
    portfolio_table.add_row("Total P&L", f"${portfolio.total_profit_loss:,.2f}")
    
    console.print(portfolio_table)
    
    # Positions
    if portfolio.positions:
        positions_table = Table(title="Current Positions")
        positions_table.add_column("Symbol", style="cyan")
        positions_table.add_column("Amount", style="green")
        
        for symbol, amount in portfolio.positions.items():
            positions_table.add_row(symbol, f"{amount:.2f}")
        
        console.print(positions_table)


def _display_performance(performance):
    """Display performance statistics."""
    perf_table = Table(title="Performance Statistics")
    perf_table.add_column("Metric", style="cyan")
    perf_table.add_column("Value", style="green")
    
    perf_table.add_row("Total Return", f"{performance['total_return']:.2f}%")
    perf_table.add_row("Profit Factor", f"{performance['profit_factor']:.2f}")
    perf_table.add_row("Max Drawdown", f"{performance['max_drawdown']:.2f}%")
    perf_table.add_row("Daily Trades", str(performance['daily_trades']))
    perf_table.add_row("Consecutive Losses", str(performance['consecutive_losses']))
    
    console.print(perf_table)


def main():
    """Main CLI entry point."""
    try:
        cli()
    except Exception as e:
        console.print(f"[bold red]CLI Error: {e}[/bold red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
