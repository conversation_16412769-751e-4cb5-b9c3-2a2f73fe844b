"""
Modern Trading Engine
====================

High-performance async trading engine with risk management.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional

from ..models.types import Portfolio, TradeAction, TradeOrder
from ..utils.config import config
from ..utils.logger import get_logger, log_trade

logger = get_logger(__name__)


class InsufficientBalanceError(Exception):
    """Raised when insufficient balance for trade."""
    pass


class TradeExecutionError(Exception):
    """Raised when trade execution fails."""
    pass


class Trader:
    """Modern async trading engine."""
    
    def __init__(self, initial_balance: float = None):
        self.initial_balance = initial_balance or config.initial_balance
        self.portfolio = Portfolio(balance=self.initial_balance)
        self.trade_history: List[TradeOrder] = []
        self.daily_trades = 0
        self.consecutive_losses = 0
        self.last_trade_time: Optional[datetime] = None
        self._lock = asyncio.Lock()
        
        logger.info(f"Trader initialized with balance: ${self.initial_balance:,.2f}")
    
    async def execute_trade(self, order: TradeOrder) -> bool:
        """Execute a trade order asynchronously."""
        async with self._lock:
            try:
                # Validate trade
                if not await self._validate_trade(order):
                    return False
                
                # Calculate trade cost/proceeds
                cost = self._calculate_trade_cost(order)
                
                # Execute the trade
                if order.action == TradeAction.BUY:
                    await self._execute_buy(order, cost)
                elif order.action == TradeAction.SELL:
                    await self._execute_sell(order, cost)
                else:
                    logger.warning(f"Invalid trade action: {order.action}")
                    return False
                
                # Update portfolio and history
                self.trade_history.append(order)
                self.daily_trades += 1
                self.last_trade_time = datetime.now()
                
                # Log trade
                log_trade(
                    action=order.action.value,
                    symbol=order.symbol,
                    amount=order.amount,
                    price=order.price or 0,
                    balance=self.portfolio.balance
                )
                
                logger.info(
                    f"Trade executed: {order.action.value.upper()} {order.amount} {order.symbol} "
                    f"@ ${order.price:.4f} | Balance: ${self.portfolio.balance:,.2f}"
                )
                
                return True
                
            except Exception as e:
                logger.error(f"Trade execution failed: {e}")
                raise TradeExecutionError(f"Failed to execute trade: {e}")
    
    async def _validate_trade(self, order: TradeOrder) -> bool:
        """Validate trade order."""
        # Check daily trade limit
        if self.daily_trades >= config.max_daily_trades:
            logger.warning(f"Daily trade limit reached: {self.daily_trades}")
            return False
        
        # Check consecutive losses
        if self.consecutive_losses >= config.max_consecutive_losses:
            logger.warning(f"Max consecutive losses reached: {self.consecutive_losses}")
            return False
        
        # Check minimum time between trades
        if self.last_trade_time:
            time_diff = (datetime.now() - self.last_trade_time).total_seconds() * 1000
            if time_diff < config.min_time_between_trades:
                logger.warning(f"Minimum time between trades not met: {time_diff}ms")
                return False
        
        # Check balance for buy orders
        if order.action == TradeAction.BUY:
            cost = self._calculate_trade_cost(order)
            if cost > self.portfolio.balance:
                logger.warning(f"Insufficient balance: ${cost:.2f} > ${self.portfolio.balance:.2f}")
                return False
        
        # Check position for sell orders
        if order.action == TradeAction.SELL:
            current_position = self.portfolio.positions.get(order.symbol, 0)
            if current_position < order.amount:
                logger.warning(f"Insufficient position: {order.amount} > {current_position}")
                return False
        
        return True
    
    def _calculate_trade_cost(self, order: TradeOrder) -> float:
        """Calculate trade cost including fees."""
        base_cost = order.amount * (order.price or 0)
        # Add spread and commission (simplified)
        spread_cost = base_cost * 0.0001  # 0.01% spread
        commission = 0.0  # No commission for now
        return base_cost + spread_cost + commission
    
    async def _execute_buy(self, order: TradeOrder, cost: float):
        """Execute buy order."""
        if cost > self.portfolio.balance:
            raise InsufficientBalanceError(f"Insufficient balance: ${cost:.2f}")
        
        # Update balance
        self.portfolio.balance -= cost
        
        # Update position
        current_position = self.portfolio.positions.get(order.symbol, 0)
        self.portfolio.positions[order.symbol] = current_position + order.amount
        
        # Update portfolio stats
        self.portfolio.total_trades += 1
    
    async def _execute_sell(self, order: TradeOrder, proceeds: float):
        """Execute sell order."""
        current_position = self.portfolio.positions.get(order.symbol, 0)
        if current_position < order.amount:
            raise InsufficientBalanceError(f"Insufficient position: {order.amount}")
        
        # Update balance
        self.portfolio.balance += proceeds
        
        # Update position
        self.portfolio.positions[order.symbol] = current_position - order.amount
        if self.portfolio.positions[order.symbol] == 0:
            del self.portfolio.positions[order.symbol]
        
        # Update portfolio stats
        self.portfolio.total_trades += 1
        
        # Calculate P&L for this trade
        # This is simplified - in reality you'd track individual position costs
        profit_loss = proceeds - (order.amount * (order.price or 0))
        self.portfolio.total_profit_loss += profit_loss
        
        if profit_loss > 0:
            self.portfolio.winning_trades += 1
            self.consecutive_losses = 0
        else:
            self.portfolio.losing_trades += 1
            self.consecutive_losses += 1
    
    def get_portfolio(self) -> Portfolio:
        """Get current portfolio state."""
        return self.portfolio.model_copy()
    
    def get_balance(self) -> float:
        """Get current balance."""
        return self.portfolio.balance
    
    def get_position(self, symbol: str) -> float:
        """Get position size for symbol."""
        return self.portfolio.positions.get(symbol, 0)
    
    def get_trade_history(self, limit: Optional[int] = None) -> List[TradeOrder]:
        """Get trade history."""
        if limit:
            return self.trade_history[-limit:]
        return self.trade_history.copy()
    
    def reset_daily_counters(self):
        """Reset daily trading counters."""
        self.daily_trades = 0
        logger.debug("Daily trade counters reset")
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get performance statistics."""
        return {
            "balance": self.portfolio.balance,
            "total_return": ((self.portfolio.balance - self.initial_balance) / self.initial_balance) * 100,
            "total_trades": self.portfolio.total_trades,
            "win_rate": self.portfolio.win_rate,
            "profit_factor": self.portfolio.profit_factor,
            "total_profit_loss": self.portfolio.total_profit_loss,
            "max_drawdown": self.portfolio.max_drawdown,
            "daily_trades": self.daily_trades,
            "consecutive_losses": self.consecutive_losses,
        }


__all__ = ["Trader", "InsufficientBalanceError", "TradeExecutionError"]
