"""
Alpha Vantage Data Fetcher for Live Trading
==========================================

🚀 ZACHRANA LUDSTVA - WORKING API DATA! 🚀
"""

import asyncio
import httpx
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import pandas as pd

from ..models.types import MarketData
from ..utils.config import config
from ..utils.logger import get_logger, log_api_call

logger = get_logger(__name__)


class AlphaVantageDataFetcher:
    """Alpha Vantage data fetcher for live forex data."""
    
    def __init__(self):
        self.api_key = config.alpha_vantage_api_key or "demo"
        self.base_url = "https://www.alphavantage.co/query"
        self.client = httpx.AsyncClient(timeout=30.0)
        
        logger.info(f"🚀 Alpha Vantage fetcher initialized with API key: {self.api_key[:4]}...")
    
    async def fetch_forex_data(self, 
                              from_symbol: str = "EUR", 
                              to_symbol: str = "USD",
                              interval: str = "5min") -> List[MarketData]:
        """Fetch live forex data from Alpha Vantage."""
        
        try:
            params = {
                "function": "FX_INTRADAY",
                "from_symbol": from_symbol,
                "to_symbol": to_symbol,
                "interval": interval,
                "apikey": self.api_key,
                "outputsize": "compact"
            }
            
            logger.info(f"Fetching {from_symbol}/{to_symbol} data from Alpha Vantage...")
            
            start_time = datetime.now()
            response = await self.client.get(self.base_url, params=params)
            end_time = datetime.now()
            
            # Log API call
            log_api_call(
                "Alpha Vantage",
                response.status_code,
                (end_time - start_time).total_seconds() * 1000
            )
            
            if response.status_code != 200:
                logger.error(f"Alpha Vantage API error: {response.status_code}")
                return []
            
            data = response.json()

            # 🚀 ZACHRANA LUDSTVA - DEBUG RESPONSE!
            logger.info(f"Alpha Vantage full response: {data}")

            # Check for API errors
            if "Error Message" in data:
                logger.error(f"Alpha Vantage error: {data['Error Message']}")
                return []

            if "Note" in data:
                logger.warning(f"Alpha Vantage note: {data['Note']}")
                return []

            if "Information" in data and len(data) == 1:
                logger.warning(f"Only Information key found: {data['Information']}")
                return []
            
            # Parse time series data - 🚀 ZACHRANA LUDSTVA DEBUG!
            logger.info(f"Alpha Vantage response keys: {list(data.keys())}")

            time_series_key = f"Time Series FX ({interval})"
            if time_series_key not in data:
                # Try alternative key formats
                possible_keys = [k for k in data.keys() if "Time Series" in k]
                if possible_keys:
                    time_series_key = possible_keys[0]
                    logger.info(f"Using alternative key: {time_series_key}")
                else:
                    logger.error(f"No time series data found. Keys: {list(data.keys())}")
                    return []
            
            time_series = data[time_series_key]
            market_data = []
            
            for timestamp_str, ohlc in time_series.items():
                try:
                    timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                    
                    market_data.append(MarketData(
                        symbol=f"{from_symbol}/{to_symbol}",
                        timestamp=timestamp,
                        open=float(ohlc["1. open"]),
                        high=float(ohlc["2. high"]),
                        low=float(ohlc["3. low"]),
                        close=float(ohlc["4. close"]),
                        volume=1000.0,  # Alpha Vantage forex doesn't provide volume
                        source="Alpha Vantage"
                    ))
                    
                except (ValueError, KeyError) as e:
                    logger.warning(f"Error parsing data point {timestamp_str}: {e}")
                    continue
            
            # Sort by timestamp (newest first)
            market_data.sort(key=lambda x: x.timestamp, reverse=True)
            
            logger.info(f"✅ Fetched {len(market_data)} data points from Alpha Vantage")
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error fetching Alpha Vantage data: {e}")
            return []
    
    async def fetch_latest_price(self, 
                                from_symbol: str = "EUR", 
                                to_symbol: str = "USD") -> Optional[MarketData]:
        """Fetch latest forex price."""
        
        try:
            params = {
                "function": "CURRENCY_EXCHANGE_RATE",
                "from_currency": from_symbol,
                "to_currency": to_symbol,
                "apikey": self.api_key
            }
            
            start_time = datetime.now()
            response = await self.client.get(self.base_url, params=params)
            end_time = datetime.now()
            
            # Log API call
            log_api_call(
                "Alpha Vantage Real-time",
                response.status_code,
                (end_time - start_time).total_seconds() * 1000
            )
            
            if response.status_code != 200:
                logger.error(f"Alpha Vantage real-time API error: {response.status_code}")
                return None
            
            data = response.json()
            
            if "Error Message" in data:
                logger.error(f"Alpha Vantage real-time error: {data['Error Message']}")
                return None
            
            # Parse real-time data
            if "Realtime Currency Exchange Rate" not in data:
                logger.error(f"No real-time data found. Keys: {list(data.keys())}")
                return None
            
            rate_data = data["Realtime Currency Exchange Rate"]
            
            price = float(rate_data["5. Exchange Rate"])
            timestamp_str = rate_data["6. Last Refreshed"]
            
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                timestamp = datetime.now()
            
            market_data = MarketData(
                symbol=f"{from_symbol}/{to_symbol}",
                timestamp=timestamp,
                open=price,
                high=price,
                low=price,
                close=price,
                volume=1000.0,
                source="Alpha Vantage Real-time"
            )
            
            logger.info(f"✅ Latest {from_symbol}/{to_symbol}: {price:.5f}")
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error fetching latest Alpha Vantage price: {e}")
            return None
    
    async def close(self):
        """Close HTTP client."""
        await self.client.aclose()
        logger.info("Alpha Vantage client closed")


# Test function
async def test_alpha_vantage():
    """Test Alpha Vantage fetcher."""
    fetcher = AlphaVantageDataFetcher()
    
    try:
        # Test forex data
        data = await fetcher.fetch_forex_data("EUR", "USD", "5min")
        print(f"Fetched {len(data)} data points")
        
        if data:
            latest = data[0]
            print(f"Latest: {latest.symbol} @ {latest.close:.5f} ({latest.timestamp})")
        
        # Test latest price
        latest_price = await fetcher.fetch_latest_price("EUR", "USD")
        if latest_price:
            print(f"Real-time: {latest_price.symbol} @ {latest_price.close:.5f}")
        
    finally:
        await fetcher.close()


if __name__ == "__main__":
    asyncio.run(test_alpha_vantage())


__all__ = ["AlphaVantageDataFetcher"]
