"""
Modern Data Fetcher
==================

High-performance async data fetcher with multiple data sources and caching.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union

import httpx
import pandas as pd
import yfinance as yf
# from alpha_vantage.foreignexchange import ForeignExchange
# from alpha_vantage.timeseries import TimeSeries

from ..models.types import MarketData
from ..utils.config import config
from ..utils.logger import get_logger, log_api_call, log_market_data

logger = get_logger(__name__)


class DataFetchError(Exception):
    """Raised when data fetching fails."""
    pass


class DataFetcher:
    """Modern async data fetcher with multiple sources."""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
        self.cache: Dict[str, Dict] = {}
        self.cache_ttl = 60  # seconds
        
        # Initialize API clients (Alpha Vantage disabled for now)
        self.alpha_vantage_fx = None
        self.alpha_vantage_ts = None
        
        logger.info("DataFetcher initialized with async HTTP client")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
    
    async def fetch_market_data(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        interval: str = "1d"
    ) -> List[MarketData]:
        """Fetch market data from multiple sources with fallback."""
        cache_key = f"{symbol}_{start_date}_{end_date}_{interval}"
        
        # Check cache first
        if self._is_cached(cache_key):
            logger.debug(f"Returning cached data for {symbol}")
            return self.cache[cache_key]["data"]
        
        # Try multiple data sources
        data = None
        sources = [
            ("Yahoo Finance", self._fetch_yahoo_finance),
            ("Exchange Rate Host", self._fetch_exchange_rate_host),
        ]
        
        for source_name, fetch_func in sources:
            try:
                logger.info(f"Fetching {symbol} data from {source_name}")
                start_time = time.time()
                
                data = await fetch_func(symbol, start_date, end_date, interval)
                
                response_time = (time.time() - start_time) * 1000
                log_api_call(source_name, 200, response_time)
                
                if data:
                    logger.info(f"Successfully fetched {len(data)} data points from {source_name}")
                    break
                    
            except Exception as e:
                logger.warning(f"Failed to fetch from {source_name}: {e}")
                log_api_call(source_name, 500, 0)
                continue
        
        if not data:
            raise DataFetchError(f"Failed to fetch data for {symbol} from all sources")
        
        # Cache the result
        self.cache[cache_key] = {
            "data": data,
            "timestamp": time.time()
        }
        
        # Log market data
        if data:
            latest = data[-1]
            log_market_data(latest.symbol, latest.close)
        
        return data
    
    async def _fetch_yahoo_finance(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        interval: str
    ) -> List[MarketData]:
        """Fetch data from Yahoo Finance."""
        try:
            # Convert forex symbol format (EUR/USD -> EURUSD=X)
            if "/" in symbol:
                yf_symbol = symbol.replace("/", "") + "=X"
            else:
                yf_symbol = symbol
            
            ticker = yf.Ticker(yf_symbol)
            
            # Convert interval format
            yf_interval = self._convert_interval_to_yf(interval)
            
            # Fetch data
            df = ticker.history(
                start=start_date,
                end=end_date,
                interval=yf_interval,
                auto_adjust=True,
                prepost=True
            )
            
            if df.empty:
                return []
            
            # Convert to MarketData objects
            market_data = []
            for timestamp, row in df.iterrows():
                market_data.append(MarketData(
                    symbol=symbol,
                    timestamp=timestamp.to_pydatetime(),
                    open=float(row['Open']),
                    high=float(row['High']),
                    low=float(row['Low']),
                    close=float(row['Close']),
                    volume=float(row['Volume']) if pd.notna(row['Volume']) else None
                ))
            
            return market_data
            
        except Exception as e:
            logger.error(f"Yahoo Finance fetch error: {e}")
            raise DataFetchError(f"Yahoo Finance error: {e}")
    
    async def _fetch_alpha_vantage(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        interval: str
    ) -> List[MarketData]:
        """Fetch data from Alpha Vantage (disabled)."""
        raise DataFetchError("Alpha Vantage not available in minimal version")
    
    async def _fetch_exchange_rate_host(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        interval: str
    ) -> List[MarketData]:
        """Fetch data from Exchange Rate Host (free API)."""
        if "/" not in symbol:
            raise DataFetchError("Exchange Rate Host only supports forex pairs")
        
        try:
            base, target = symbol.split("/")
            
            url = f"https://api.exchangerate.host/timeseries"
            params = {
                "start_date": start_date,
                "end_date": end_date,
                "base": base,
                "symbols": target
            }
            
            if config.access_key:
                params["access_key"] = config.access_key
            
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if not data.get("success", False):
                raise DataFetchError(f"API error: {data.get('error', 'Unknown error')}")
            
            rates = data.get("rates", {})
            if not rates:
                return []
            
            # Convert to MarketData objects
            market_data = []
            for date_str, rate_data in rates.items():
                if target in rate_data:
                    rate = rate_data[target]
                    timestamp = datetime.strptime(date_str, "%Y-%m-%d")
                    
                    # For daily data, we only have close price
                    market_data.append(MarketData(
                        symbol=symbol,
                        timestamp=timestamp,
                        open=rate,  # Simplified - same as close
                        high=rate * 1.001,  # Estimated
                        low=rate * 0.999,   # Estimated
                        close=rate,
                        volume=None
                    ))
            
            return sorted(market_data, key=lambda x: x.timestamp)
            
        except Exception as e:
            logger.error(f"Exchange Rate Host fetch error: {e}")
            raise DataFetchError(f"Exchange Rate Host error: {e}")
    
    def _convert_interval_to_yf(self, interval: str) -> str:
        """Convert interval to Yahoo Finance format."""
        mapping = {
            "1min": "1m",
            "5min": "5m",
            "15min": "15m",
            "30min": "30m",
            "60min": "1h",
            "1h": "1h",
            "1d": "1d",
            "daily": "1d"
        }
        return mapping.get(interval, "1d")
    
    def _is_cached(self, cache_key: str) -> bool:
        """Check if data is cached and still valid."""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]["timestamp"]
        return (time.time() - cache_time) < self.cache_ttl
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for symbol."""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            
            data = await self.fetch_market_data(symbol, start_date, end_date, "1d")
            
            if data:
                return data[-1].close
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get current price for {symbol}: {e}")
            return None
    
    def clear_cache(self):
        """Clear data cache."""
        self.cache.clear()
        logger.info("Data cache cleared")


__all__ = ["DataFetcher", "DataFetchError"]
