"""
Mock Data Generator for Live Trading
===================================

🚀 ZACHRANA LUDSTVA - REALISTIC FOREX DATA! 🚀
"""

import random
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
import math

from ..models.types import MarketData
from ..utils.logger import get_logger

logger = get_logger(__name__)


class MockDataGenerator:
    """Generate realistic forex data for testing."""
    
    def __init__(self, base_price: float = 1.1720):
        self.base_price = base_price
        self.current_price = base_price
        self.trend = 0.0  # -1 to 1
        self.volatility = 0.0001  # Base volatility
        
        logger.info(f"🚀 Mock data generator initialized at {base_price}")
    
    def generate_realistic_price_movement(self) -> float:
        """Generate realistic price movement using random walk with trend."""
        
        # Random walk component
        random_change = random.gauss(0, self.volatility)
        
        # Trend component (slowly changing)
        self.trend += random.gauss(0, 0.01)
        self.trend = max(-0.5, min(0.5, self.trend))  # Clamp trend
        
        # Volatility clustering (high volatility periods)
        if random.random() < 0.05:  # 5% chance of high volatility
            self.volatility = min(0.001, self.volatility * 2)
        else:
            self.volatility = max(0.0001, self.volatility * 0.99)
        
        # Combine components
        price_change = random_change + (self.trend * 0.0001)
        
        # Update current price
        self.current_price += price_change
        
        # Keep price in reasonable range
        self.current_price = max(1.0, min(1.5, self.current_price))
        
        return self.current_price
    
    def generate_ohlc_bar(self, timestamp: datetime) -> MarketData:
        """Generate realistic OHLC bar."""
        
        # Generate 4 price points for OHLC
        prices = []
        open_price = self.current_price
        
        for _ in range(4):
            prices.append(self.generate_realistic_price_movement())
        
        close_price = prices[-1]
        high_price = max(open_price, *prices)
        low_price = min(open_price, *prices)
        
        # Generate realistic volume
        volume = random.uniform(800, 1200)
        
        return MarketData(
            symbol="EUR/USD",
            timestamp=timestamp,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume,
            source="Mock Generator"
        )
    
    async def generate_historical_data(self, 
                                     hours_back: int = 24,
                                     interval_minutes: int = 5) -> List[MarketData]:
        """Generate historical data for backtesting."""
        
        data = []
        current_time = datetime.now()
        
        # Generate data going backwards
        for i in range(hours_back * (60 // interval_minutes)):
            timestamp = current_time - timedelta(minutes=i * interval_minutes)
            bar = self.generate_ohlc_bar(timestamp)
            data.append(bar)
        
        # Reverse to get chronological order
        data.reverse()
        
        logger.info(f"✅ Generated {len(data)} historical bars")
        return data
    
    async def generate_live_tick(self) -> MarketData:
        """Generate single live tick."""
        
        timestamp = datetime.now()
        tick = self.generate_ohlc_bar(timestamp)
        
        logger.debug(f"Generated live tick: {tick.close:.5f}")
        return tick
    
    def add_news_event(self, impact: float = 0.01):
        """Simulate news event impact."""
        
        direction = random.choice([-1, 1])
        price_jump = direction * impact
        
        self.current_price += price_jump
        self.volatility *= 3  # Increase volatility after news
        
        logger.info(f"📰 News event: {direction * impact * 10000:.1f} pips impact")
    
    def simulate_market_session(self, session: str):
        """Simulate different market session characteristics."""
        
        if session == "LONDON":
            self.volatility = 0.0003  # Higher volatility
            self.trend += random.gauss(0, 0.02)
        elif session == "NEW_YORK":
            self.volatility = 0.0004  # Highest volatility
            # Simulate overlap with London
            if random.random() < 0.1:
                self.add_news_event(0.005)
        elif session == "TOKYO":
            self.volatility = 0.0002  # Lower volatility
            self.trend *= 0.9  # Dampen trends
        else:  # Off-hours
            self.volatility = 0.0001  # Very low volatility
            self.trend *= 0.95
        
        logger.debug(f"Market session: {session}, volatility: {self.volatility:.6f}")


class MockAlphaVantageDataFetcher:
    """Mock Alpha Vantage fetcher that generates realistic data."""
    
    def __init__(self):
        self.generator = MockDataGenerator()
        logger.info("🚀 Mock Alpha Vantage fetcher initialized - ZACHRANA LUDSTVA!")
    
    async def fetch_forex_data(self, 
                              from_symbol: str = "EUR", 
                              to_symbol: str = "USD",
                              interval: str = "5min") -> List[MarketData]:
        """Generate mock forex data."""
        
        # Simulate API delay
        await asyncio.sleep(0.5)
        
        # Generate 100 data points (about 8 hours of 5min data)
        data = await self.generator.generate_historical_data(hours_back=8, interval_minutes=5)
        
        logger.info(f"✅ Mock Alpha Vantage returned {len(data)} data points")
        return data
    
    async def fetch_latest_price(self, 
                                from_symbol: str = "EUR", 
                                to_symbol: str = "USD") -> MarketData:
        """Generate mock latest price."""
        
        # Simulate API delay
        await asyncio.sleep(0.2)
        
        tick = await self.generator.generate_live_tick()
        
        logger.info(f"✅ Mock latest {from_symbol}/{to_symbol}: {tick.close:.5f}")
        return tick
    
    async def close(self):
        """Mock close method."""
        logger.info("Mock Alpha Vantage client closed")


# Test function
async def test_mock_generator():
    """Test mock data generator."""
    generator = MockDataGenerator()
    
    # Generate historical data
    data = await generator.generate_historical_data(hours_back=2, interval_minutes=5)
    print(f"Generated {len(data)} historical bars")
    
    if data:
        latest = data[-1]
        print(f"Latest: {latest.symbol} @ {latest.close:.5f} ({latest.timestamp})")
    
    # Generate live ticks
    for i in range(5):
        tick = await generator.generate_live_tick()
        print(f"Live tick {i+1}: {tick.close:.5f}")
        await asyncio.sleep(0.1)


if __name__ == "__main__":
    asyncio.run(test_mock_generator())


__all__ = ["MockDataGenerator", "MockAlphaVantageDataFetcher"]
