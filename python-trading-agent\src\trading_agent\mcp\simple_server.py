#!/usr/bin/env python3
"""
Simplified Trading Server
========================

Simple HTTP server for trading operations without MCP dependency.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any

from ..core.trader import Trader
from ..data.fetcher import DataFetcher
from ..strategies.smart_strategy import SmartStrategy
from ..strategies.optimized_strategy import OptimizedStrategy
from ..utils.config import config
from ..utils.logger import get_logger

logger = get_logger(__name__)


class SimpleTradingServer:
    """Simple trading server without MCP dependency."""
    
    def __init__(self):
        self.trader = Trader()
        self.data_fetcher = DataFetcher()
        self.smart_strategy = SmartStrategy()
        self.optimized_strategy = OptimizedStrategy()
        
        logger.info("Simple Trading Server initialized")
    
    async def get_market_data(self, symbol: str = "EUR/USD", days: int = 1) -> Dict[str, Any]:
        """Get market data for a symbol."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            async with self.data_fetcher:
                market_data = await self.data_fetcher.fetch_market_data(
                    symbol=symbol,
                    start_date=start_date.strftime("%Y-%m-%d"),
                    end_date=end_date.strftime("%Y-%m-%d"),
                    interval="5min"
                )
            
            # Convert to JSON-serializable format
            data_dict = []
            for data_point in market_data[-10:]:  # Last 10 points
                data_dict.append({
                    "symbol": data_point.symbol,
                    "timestamp": data_point.timestamp.isoformat(),
                    "open": data_point.open,
                    "high": data_point.high,
                    "low": data_point.low,
                    "close": data_point.close,
                    "volume": data_point.volume
                })
            
            return {
                "success": True,
                "symbol": symbol,
                "data_points": len(data_dict),
                "data": data_dict
            }
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return {"success": False, "error": str(e)}
    
    async def analyze_strategy(self, symbol: str = "EUR/USD", strategy: str = "smart") -> Dict[str, Any]:
        """Analyze trading strategy."""
        try:
            # Get market data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=1)
            
            async with self.data_fetcher:
                market_data = await self.data_fetcher.fetch_market_data(
                    symbol=symbol,
                    start_date=start_date.strftime("%Y-%m-%d"),
                    end_date=end_date.strftime("%Y-%m-%d"),
                    interval="5min"
                )
            
            if not market_data:
                return {"success": False, "error": f"No market data for {symbol}"}
            
            # Choose strategy
            if strategy == "optimized":
                strategy_instance = self.optimized_strategy
            else:
                strategy_instance = self.smart_strategy
            
            # Analyze
            analysis = await strategy_instance.analyze(market_data)
            
            # Generate trade decision
            trade_order = await strategy_instance.decide_action(analysis)
            
            return {
                "success": True,
                "symbol": symbol,
                "strategy": strategy,
                "analysis": {
                    "signals": {
                        "buy": analysis.signals.buy,
                        "sell": analysis.signals.sell,
                        "confidence": analysis.signals.confidence,
                        "quality": analysis.signals.quality.value,
                    },
                    "recommendation": analysis.recommendation.value,
                    "risk_level": analysis.risk_level.value,
                    "reasoning": analysis.reasoning
                },
                "trade_decision": {
                    "action": trade_order.action.value if trade_order else "hold",
                    "amount": trade_order.amount if trade_order else 0,
                } if trade_order else None
            }
            
        except Exception as e:
            logger.error(f"Error analyzing strategy: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_portfolio(self) -> Dict[str, Any]:
        """Get portfolio status."""
        try:
            portfolio = self.trader.get_portfolio()
            performance = self.trader.get_performance_stats()
            
            return {
                "success": True,
                "portfolio": {
                    "balance": portfolio.balance,
                    "positions": portfolio.positions,
                    "total_trades": portfolio.total_trades,
                    "win_rate": portfolio.win_rate,
                },
                "performance": performance
            }
            
        except Exception as e:
            logger.error(f"Error getting portfolio: {e}")
            return {"success": False, "error": str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """Check server health."""
        try:
            # Test data fetcher
            async with self.data_fetcher:
                current_price = await self.data_fetcher.get_current_price("EUR/USD")
            
            return {
                "success": True,
                "status": "healthy",
                "server": "python-trading-agent",
                "version": "2.0.0",
                "timestamp": datetime.now().isoformat(),
                "components": {
                    "trader": "operational",
                    "data_fetcher": "operational" if current_price else "degraded",
                    "strategies": "operational"
                },
                "test_price": current_price
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "success": False,
                "status": "unhealthy",
                "error": str(e)
            }


async def main():
    """Main entry point for simple server."""
    server = SimpleTradingServer()
    
    print("🚀 Python Trading Agent - Simple Server")
    print("=" * 50)
    
    # Run health check
    health = await server.health_check()
    print(f"Health Status: {health['status']}")
    
    if health['success']:
        # Test market data
        print("\n📊 Testing Market Data...")
        market_data = await server.get_market_data("EUR/USD", 1)
        if market_data['success']:
            print(f"✓ Got {market_data['data_points']} data points for EUR/USD")
            if market_data['data']:
                latest = market_data['data'][-1]
                print(f"  Latest: {latest['close']:.4f} at {latest['timestamp']}")
        else:
            print(f"✗ Market data failed: {market_data['error']}")
        
        # Test strategy analysis
        print("\n🧠 Testing Strategy Analysis...")
        analysis = await server.analyze_strategy("EUR/USD", "smart")
        if analysis['success']:
            print(f"✓ Strategy analysis completed")
            print(f"  Recommendation: {analysis['analysis']['recommendation']}")
            print(f"  Confidence: {analysis['analysis']['signals']['confidence']:.1f}%")
            print(f"  Trade Decision: {analysis['trade_decision']['action'] if analysis['trade_decision'] else 'HOLD'}")
        else:
            print(f"✗ Strategy analysis failed: {analysis['error']}")
        
        # Test portfolio
        print("\n💼 Testing Portfolio...")
        portfolio = await server.get_portfolio()
        if portfolio['success']:
            print(f"✓ Portfolio status retrieved")
            print(f"  Balance: ${portfolio['portfolio']['balance']:,.2f}")
            print(f"  Total Trades: {portfolio['portfolio']['total_trades']}")
        else:
            print(f"✗ Portfolio failed: {portfolio['error']}")
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
