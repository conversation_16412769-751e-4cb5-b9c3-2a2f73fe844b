"""
Ensemble ML Models for 80% Win Rate
==================================

🚀 ZACHRANA LUDSTVA - ENSEMBLE POWER! 🚀
XGBoost + Random Forest + LightGBM + CatBoost
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import joblib
import os

try:
    from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
    from sklearn.model_selection import cross_val_score, GridSearchCV
    from sklearn.metrics import accuracy_score, classification_report
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

from ..utils.logger import get_logger

logger = get_logger(__name__)


class EnsemblePredictor:
    """Ensemble of ML models for maximum 80% win rate."""
    
    def __init__(self):
        self.models = {}
        self.is_trained = False
        self.feature_importance = {}
        
        # Initialize models if libraries available
        if SKLEARN_AVAILABLE:
            self.models['random_forest'] = RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        
        if XGBOOST_AVAILABLE:
            self.models['xgboost'] = xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            )
        
        if LIGHTGBM_AVAILABLE:
            self.models['lightgbm'] = lgb.LGBMClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            )
        
        # 🚀 CATBOOST DOČASNE VYPNUTÝ - PRÍLIŠ POMALÝ!
        # if CATBOOST_AVAILABLE:
        #     self.models['catboost'] = cb.CatBoostClassifier(
        #         iterations=200,
        #         depth=6,
        #         learning_rate=0.1,
        #         random_seed=42,
        #         verbose=False
        #     )
        
        logger.info(f"🚀 Ensemble initialized with {len(self.models)} models!")
        logger.info(f"Available models: {list(self.models.keys())}")
    
    def prepare_targets(self, prices: np.ndarray, threshold: float = 0.00001) -> np.ndarray:  # 🚀 100x MENŠÍ THRESHOLD!
        """Prepare classification targets (buy=1, sell=0, hold=2)."""
        
        # Calculate future returns
        future_returns = np.diff(prices) / prices[:-1]
        
        # Create targets
        targets = np.zeros(len(future_returns))
        targets[future_returns > threshold] = 1  # Buy signal
        targets[future_returns < -threshold] = 0  # Sell signal
        targets[abs(future_returns) <= threshold] = 2  # Hold signal
        
        # Add one more target for last price (hold)
        targets = np.append(targets, 2)
        
        logger.info(f"Target distribution: Buy={np.sum(targets==1)}, Sell={np.sum(targets==0)}, Hold={np.sum(targets==2)}")
        
        return targets.astype(int)
    
    def train(self, X: np.ndarray, y: np.ndarray, validation_split: float = 0.2) -> Dict[str, float]:
        """Train all ensemble models."""
        
        logger.info(f"🚀 Training ensemble for 80% win rate...")
        logger.info(f"Training data: X {X.shape}, y {y.shape}")
        
        if len(self.models) == 0:
            logger.error("No ML libraries available! Cannot train ensemble.")
            return {"ensemble_accuracy": 0.0}
        
        # Split data
        split_idx = int(len(X) * (1 - validation_split))
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        results = {}
        
        # Train each model
        for name, model in self.models.items():
            try:
                logger.info(f"Training {name}...")
                
                # Train model
                model.fit(X_train, y_train)
                
                # Validate
                y_pred = model.predict(X_val)
                accuracy = accuracy_score(y_val, y_pred)
                
                results[f"{name}_accuracy"] = accuracy
                
                # Feature importance
                if hasattr(model, 'feature_importances_'):
                    self.feature_importance[name] = model.feature_importances_
                
                logger.info(f"✅ {name} accuracy: {accuracy:.4f}")
                
            except Exception as e:
                logger.error(f"Error training {name}: {e}")
                results[f"{name}_accuracy"] = 0.0
        
        # Calculate ensemble accuracy
        if len(results) > 0:
            ensemble_accuracy = np.mean(list(results.values()))
            results["ensemble_accuracy"] = ensemble_accuracy
            
            logger.info(f"🎯 Ensemble accuracy: {ensemble_accuracy:.4f}")
            
            if ensemble_accuracy >= 0.8:
                logger.info("🎉 80% WIN RATE ACHIEVED!")
            else:
                logger.info(f"📈 Need {0.8 - ensemble_accuracy:.4f} more for 80% target")
        
        self.is_trained = True
        return results
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make ensemble predictions."""
        
        if not self.is_trained:
            logger.warning("Ensemble not trained! Returning random predictions.")
            return np.random.randint(0, 3, len(X))
        
        if len(self.models) == 0:
            logger.warning("No models available! Returning random predictions.")
            return np.random.randint(0, 3, len(X))
        
        # Collect predictions from all models
        predictions = []
        
        for name, model in self.models.items():
            try:
                pred = model.predict(X)
                predictions.append(pred)
            except Exception as e:
                logger.error(f"Error predicting with {name}: {e}")
                # Add random predictions as fallback
                predictions.append(np.random.randint(0, 3, len(X)))
        
        if len(predictions) == 0:
            return np.random.randint(0, 3, len(X))
        
        # Ensemble voting
        predictions = np.array(predictions)
        
        # Majority voting
        ensemble_pred = []
        for i in range(len(X)):
            votes = predictions[:, i]
            # Get most common prediction
            unique, counts = np.unique(votes, return_counts=True)
            majority_pred = unique[np.argmax(counts)]
            ensemble_pred.append(majority_pred)
        
        return np.array(ensemble_pred)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Get prediction probabilities."""
        
        if not self.is_trained or len(self.models) == 0:
            # Return uniform probabilities
            return np.full((len(X), 3), 1/3)
        
        # Collect probabilities from all models
        all_probas = []
        
        for name, model in self.models.items():
            try:
                if hasattr(model, 'predict_proba'):
                    proba = model.predict_proba(X)
                    all_probas.append(proba)
            except Exception as e:
                logger.error(f"Error getting probabilities from {name}: {e}")
        
        if len(all_probas) == 0:
            return np.full((len(X), 3), 1/3)
        
        # Average probabilities
        ensemble_proba = np.mean(all_probas, axis=0)
        
        return ensemble_proba
    
    def get_signal_confidence(self, X: np.ndarray) -> Tuple[int, float]:
        """Get trading signal and confidence."""
        
        if len(X.shape) == 1:
            X = X.reshape(1, -1)
        
        # Get predictions and probabilities
        predictions = self.predict(X)
        probabilities = self.predict_proba(X)
        
        if len(predictions) == 0:
            return 2, 50.0  # Hold with neutral confidence
        
        # Get signal for last prediction
        signal = predictions[-1]
        
        # Get confidence (max probability * 100)
        confidence = np.max(probabilities[-1]) * 100
        
        return int(signal), float(confidence)
    
    def optimize_hyperparameters(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """Optimize hyperparameters for better performance."""
        
        logger.info("🔧 Optimizing hyperparameters for 80% win rate...")
        
        optimization_results = {}
        
        # Optimize Random Forest
        if 'random_forest' in self.models and SKLEARN_AVAILABLE:
            try:
                rf_params = {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [5, 10, 15],
                    'min_samples_split': [2, 5, 10]
                }
                
                rf_grid = GridSearchCV(
                    RandomForestClassifier(random_state=42),
                    rf_params,
                    cv=3,
                    scoring='accuracy',
                    n_jobs=-1
                )
                
                rf_grid.fit(X, y)
                self.models['random_forest'] = rf_grid.best_estimator_
                optimization_results['random_forest'] = rf_grid.best_score_
                
                logger.info(f"✅ Random Forest optimized: {rf_grid.best_score_:.4f}")
                
            except Exception as e:
                logger.error(f"Error optimizing Random Forest: {e}")
        
        # Optimize XGBoost
        if 'xgboost' in self.models and XGBOOST_AVAILABLE:
            try:
                xgb_params = {
                    'n_estimators': [100, 200],
                    'max_depth': [3, 6, 9],
                    'learning_rate': [0.01, 0.1, 0.2]
                }
                
                xgb_grid = GridSearchCV(
                    xgb.XGBClassifier(random_state=42),
                    xgb_params,
                    cv=3,
                    scoring='accuracy',
                    n_jobs=-1
                )
                
                xgb_grid.fit(X, y)
                self.models['xgboost'] = xgb_grid.best_estimator_
                optimization_results['xgboost'] = xgb_grid.best_score_
                
                logger.info(f"✅ XGBoost optimized: {xgb_grid.best_score_:.4f}")
                
            except Exception as e:
                logger.error(f"Error optimizing XGBoost: {e}")
        
        return optimization_results
    
    def save_models(self, directory: str):
        """Save all trained models."""
        os.makedirs(directory, exist_ok=True)
        
        for name, model in self.models.items():
            try:
                filepath = os.path.join(directory, f"{name}_model.pkl")
                joblib.dump(model, filepath)
                logger.info(f"Saved {name} to {filepath}")
            except Exception as e:
                logger.error(f"Error saving {name}: {e}")
    
    def load_models(self, directory: str):
        """Load trained models."""
        for name in list(self.models.keys()):
            try:
                filepath = os.path.join(directory, f"{name}_model.pkl")
                if os.path.exists(filepath):
                    self.models[name] = joblib.load(filepath)
                    logger.info(f"Loaded {name} from {filepath}")
            except Exception as e:
                logger.error(f"Error loading {name}: {e}")
        
        if len(self.models) > 0:
            self.is_trained = True


__all__ = ["EnsemblePredictor"]
