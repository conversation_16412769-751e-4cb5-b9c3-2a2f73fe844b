"""
LSTM Neural Network for Price Prediction
=======================================

🚀 ZACHRANA LUDSTVA - DEEP LEARNING FOR 80% WIN RATE! 🚀
"""

import numpy as np
import pandas as pd
from typing import Tuple, List, Optional
import joblib
import os

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

from ..utils.logger import get_logger

logger = get_logger(__name__)


class LSTMPredictor:
    """LSTM Neural Network for price prediction with 80% accuracy target."""
    
    def __init__(self, 
                 sequence_length: int = 60,
                 prediction_horizon: int = 1,
                 lstm_units: List[int] = [128, 64, 32],
                 dropout_rate: float = 0.2):
        
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available! Using dummy predictor.")
            self.model = None
            return
        
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.lstm_units = lstm_units
        self.dropout_rate = dropout_rate
        self.model = None
        self.is_trained = False
        self.scaler_X = None
        self.scaler_y = None
        
        logger.info(f"🧠 LSTM Predictor initialized for 80% win rate!")
        logger.info(f"Sequence length: {sequence_length}, LSTM units: {lstm_units}")
    
    def _build_model(self, input_shape: Tuple[int, int]):
        """Build LSTM model architecture."""
        if not TENSORFLOW_AVAILABLE:
            return None

        model = Sequential()
        
        # First LSTM layer
        model.add(LSTM(
            units=self.lstm_units[0],
            return_sequences=True,
            input_shape=input_shape,
            name='lstm_1'
        ))
        model.add(BatchNormalization())
        model.add(Dropout(self.dropout_rate))
        
        # Second LSTM layer
        if len(self.lstm_units) > 1:
            model.add(LSTM(
                units=self.lstm_units[1],
                return_sequences=len(self.lstm_units) > 2,
                name='lstm_2'
            ))
            model.add(BatchNormalization())
            model.add(Dropout(self.dropout_rate))
        
        # Third LSTM layer (optional)
        if len(self.lstm_units) > 2:
            model.add(LSTM(
                units=self.lstm_units[2],
                return_sequences=False,
                name='lstm_3'
            ))
            model.add(BatchNormalization())
            model.add(Dropout(self.dropout_rate))
        
        # Dense layers
        model.add(Dense(50, activation='relu', name='dense_1'))
        model.add(Dropout(self.dropout_rate))
        model.add(Dense(25, activation='relu', name='dense_2'))
        model.add(Dense(self.prediction_horizon, activation='linear', name='output'))
        
        # Compile model
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae', 'mape']
        )
        
        logger.info(f"✅ LSTM model built with {model.count_params()} parameters")
        return model
    
    def prepare_sequences(self, data: np.ndarray, target: np.ndarray = None) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for LSTM training/prediction."""
        X, y = [], []
        
        for i in range(self.sequence_length, len(data)):
            X.append(data[i-self.sequence_length:i])
            if target is not None:
                if i + self.prediction_horizon <= len(target):
                    y.append(target[i:i+self.prediction_horizon])
        
        X = np.array(X)
        y = np.array(y) if target is not None else None
        
        logger.info(f"Prepared sequences: X shape {X.shape}, y shape {y.shape if y is not None else 'None'}")
        return X, y
    
    def train(self, 
              X: np.ndarray, 
              y: np.ndarray,
              validation_split: float = 0.2,
              epochs: int = 100,
              batch_size: int = 32,
              verbose: int = 1) -> dict:
        """Train LSTM model."""
        
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available! Cannot train LSTM.")
            return {"loss": 0.0, "val_loss": 0.0}
        
        logger.info(f"🚀 Training LSTM for 80% win rate...")
        logger.info(f"Training data: X {X.shape}, y {y.shape}")
        
        # Prepare sequences
        X_seq, y_seq = self.prepare_sequences(X, y)
        
        if len(X_seq) == 0:
            logger.error("No sequences prepared! Check data length.")
            return {"loss": float('inf'), "val_loss": float('inf')}
        
        # Build model
        input_shape = (X_seq.shape[1], X_seq.shape[2])
        self.model = self._build_model(input_shape)
        
        # Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        # Train model
        history = self.model.fit(
            X_seq, y_seq,
            validation_split=validation_split,
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=verbose
        )
        
        self.is_trained = True
        
        # Get final metrics
        final_loss = history.history['loss'][-1]
        final_val_loss = history.history['val_loss'][-1]
        
        logger.info(f"✅ LSTM training completed!")
        logger.info(f"Final loss: {final_loss:.6f}, Val loss: {final_val_loss:.6f}")
        
        return {
            "loss": final_loss,
            "val_loss": final_val_loss,
            "history": history.history
        }
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using trained LSTM."""
        
        if not TENSORFLOW_AVAILABLE or self.model is None:
            logger.warning("LSTM not available! Returning dummy predictions.")
            return np.random.randn(len(X), self.prediction_horizon) * 0.001
        
        if not self.is_trained:
            logger.warning("LSTM not trained! Returning dummy predictions.")
            return np.random.randn(len(X), self.prediction_horizon) * 0.001
        
        # Prepare sequences
        X_seq, _ = self.prepare_sequences(X)
        
        if len(X_seq) == 0:
            logger.warning("No sequences for prediction! Returning zeros.")
            return np.zeros((1, self.prediction_horizon))
        
        # Make predictions
        predictions = self.model.predict(X_seq, verbose=0)
        
        logger.info(f"LSTM predictions: {predictions.shape}")
        return predictions
    
    def predict_next_price(self, recent_data: np.ndarray) -> float:
        """Predict next price movement."""
        
        if len(recent_data) < self.sequence_length:
            logger.warning(f"Not enough data for prediction. Need {self.sequence_length}, got {len(recent_data)}")
            return 0.0
        
        # Use last sequence_length points
        X = recent_data[-self.sequence_length:].reshape(1, -1)
        
        # Predict
        prediction = self.predict(X)
        
        if len(prediction) > 0:
            return float(prediction[0, 0])  # Return first prediction
        else:
            return 0.0
    
    def get_prediction_confidence(self, recent_data: np.ndarray) -> float:
        """Get confidence score for prediction (0-100)."""
        
        if not TENSORFLOW_AVAILABLE or self.model is None or not self.is_trained:
            return 50.0  # Neutral confidence
        
        try:
            # Make multiple predictions with dropout (Monte Carlo)
            predictions = []
            for _ in range(10):
                pred = self.predict_next_price(recent_data)
                predictions.append(pred)
            
            # Calculate confidence based on prediction variance
            pred_std = np.std(predictions)
            pred_mean = np.mean(predictions)
            
            # Lower variance = higher confidence
            if pred_std == 0:
                confidence = 95.0
            else:
                # Normalize confidence (this is a heuristic)
                confidence = max(50.0, min(95.0, 80.0 - (pred_std / abs(pred_mean + 1e-8)) * 1000))
            
            return confidence
            
        except Exception as e:
            logger.error(f"Error calculating prediction confidence: {e}")
            return 50.0
    
    def save_model(self, filepath: str):
        """Save trained LSTM model."""
        if self.model is not None and TENSORFLOW_AVAILABLE:
            self.model.save(filepath)
            logger.info(f"LSTM model saved to {filepath}")
        else:
            logger.warning("No model to save!")
    
    def load_model(self, filepath: str):
        """Load trained LSTM model."""
        if TENSORFLOW_AVAILABLE and os.path.exists(filepath):
            self.model = load_model(filepath)
            self.is_trained = True
            logger.info(f"LSTM model loaded from {filepath}")
        else:
            logger.warning(f"Cannot load model from {filepath}")
    
    def evaluate_model(self, X_test: np.ndarray, y_test: np.ndarray) -> dict:
        """Evaluate model performance."""
        
        if not TENSORFLOW_AVAILABLE or self.model is None:
            return {"mse": float('inf'), "mae": float('inf'), "mape": float('inf')}
        
        X_seq, y_seq = self.prepare_sequences(X_test, y_test)
        
        if len(X_seq) == 0:
            return {"mse": float('inf'), "mae": float('inf'), "mape": float('inf')}
        
        # Evaluate
        results = self.model.evaluate(X_seq, y_seq, verbose=0)
        
        metrics = {
            "mse": results[0],
            "mae": results[1],
            "mape": results[2] if len(results) > 2 else 0.0
        }
        
        logger.info(f"LSTM evaluation: {metrics}")
        return metrics


__all__ = ["LSTMPredictor"]
