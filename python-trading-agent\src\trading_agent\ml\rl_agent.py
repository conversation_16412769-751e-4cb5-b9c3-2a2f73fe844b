"""
Reinforcement Learning Trading Agent
===================================

🚀 ZACHRANA LUDSTVA - SELF-LEARNING AI TRADER! 🚀
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import os

try:
    import gymnasium as gym
    from gymnasium import spaces
    from stable_baselines3 import PPO, A2C, DQN
    from stable_baselines3.common.env_util import make_vec_env
    from stable_baselines3.common.callbacks import EvalCallback
    RL_AVAILABLE = True
except ImportError:
    RL_AVAILABLE = False

from ..utils.logger import get_logger

logger = get_logger(__name__)


class TradingEnvironment:
    """Custom trading environment for RL agent."""

    def __init__(self, data: np.ndarray, initial_balance: float = 10000.0):
        if not RL_AVAILABLE:
            logger.warning("RL libraries not available!")
            self.data = data
            self.initial_balance = initial_balance
            self.current_step = 0
            self.max_steps = len(data) - 1 if len(data) > 0 else 0
            return
        
        self.data = data
        self.initial_balance = initial_balance
        self.current_step = 0
        self.max_steps = len(data) - 1
        
        # Action space: 0=Hold, 1=Buy, 2=Sell
        if RL_AVAILABLE:
            self.action_space = spaces.Discrete(3)

            # Observation space: features + portfolio state
            self.observation_space = spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(data.shape[1] + 3,),  # features + balance + position + profit
                dtype=np.float32
            )
        else:
            self.action_space = None
            self.observation_space = None
        
        # Trading state
        self.balance = initial_balance
        self.position = 0  # 0=no position, 1=long, -1=short
        self.entry_price = 0.0
        self.total_profit = 0.0
        self.trades_count = 0
        self.winning_trades = 0
        
        logger.info(f"🤖 Trading Environment initialized: {data.shape[0]} steps")
    
    def reset(self, seed=None, options=None):
        """Reset environment to initial state."""
        super().reset(seed=seed)
        
        self.current_step = 0
        self.balance = self.initial_balance
        self.position = 0
        self.entry_price = 0.0
        self.total_profit = 0.0
        self.trades_count = 0
        self.winning_trades = 0
        
        return self._get_observation(), {}
    
    def step(self, action):
        """Execute one trading step."""
        
        # Get current price (assume last feature is price)
        current_price = self.data[self.current_step, -1]
        
        # Execute action
        reward = self._execute_action(action, current_price)
        
        # Move to next step
        self.current_step += 1
        
        # Check if episode is done
        done = self.current_step >= self.max_steps
        
        # Get new observation
        obs = self._get_observation()
        
        # Additional info
        info = {
            "balance": self.balance,
            "position": self.position,
            "total_profit": self.total_profit,
            "trades_count": self.trades_count,
            "win_rate": self.winning_trades / max(self.trades_count, 1) * 100
        }
        
        return obs, reward, done, False, info
    
    def _execute_action(self, action: int, current_price: float) -> float:
        """Execute trading action and return reward."""
        
        reward = 0.0
        
        if action == 1:  # Buy
            if self.position == 0:  # Open long position
                self.position = 1
                self.entry_price = current_price
                reward = -0.001  # Small penalty for transaction cost
            elif self.position == -1:  # Close short position
                profit = (self.entry_price - current_price) / self.entry_price
                self.balance += self.balance * profit
                self.total_profit += profit
                self.trades_count += 1
                if profit > 0:
                    self.winning_trades += 1
                    reward = profit * 100  # Reward for profitable trade
                else:
                    reward = profit * 50   # Smaller penalty for loss
                
                # Open new long position
                self.position = 1
                self.entry_price = current_price
        
        elif action == 2:  # Sell
            if self.position == 0:  # Open short position
                self.position = -1
                self.entry_price = current_price
                reward = -0.001  # Small penalty for transaction cost
            elif self.position == 1:  # Close long position
                profit = (current_price - self.entry_price) / self.entry_price
                self.balance += self.balance * profit
                self.total_profit += profit
                self.trades_count += 1
                if profit > 0:
                    self.winning_trades += 1
                    reward = profit * 100  # Reward for profitable trade
                else:
                    reward = profit * 50   # Smaller penalty for loss
                
                # Open new short position
                self.position = -1
                self.entry_price = current_price
        
        # Action 0 (Hold) gets no immediate reward
        
        # Add small reward for maintaining profitable position
        if self.position != 0:
            unrealized_profit = 0
            if self.position == 1:  # Long position
                unrealized_profit = (current_price - self.entry_price) / self.entry_price
            elif self.position == -1:  # Short position
                unrealized_profit = (self.entry_price - current_price) / self.entry_price
            
            reward += unrealized_profit * 0.1  # Small reward for unrealized profit
        
        return reward
    
    def _get_observation(self) -> np.ndarray:
        """Get current observation."""
        if self.current_step >= len(self.data):
            # Return last observation if we're at the end
            features = self.data[-1]
        else:
            features = self.data[self.current_step]
        
        # Add portfolio state
        portfolio_state = np.array([
            self.balance / self.initial_balance,  # Normalized balance
            self.position,  # Current position
            self.total_profit  # Total profit
        ])
        
        observation = np.concatenate([features, portfolio_state]).astype(np.float32)
        return observation


class RLTradingAgent:
    """Reinforcement Learning trading agent for 80% win rate."""
    
    def __init__(self, algorithm: str = "PPO"):
        
        if not RL_AVAILABLE:
            logger.warning("RL libraries not available! Using dummy agent.")
            self.agent = None
            return
        
        self.algorithm = algorithm
        self.agent = None
        self.env = None
        self.is_trained = False
        
        logger.info(f"🤖 RL Trading Agent initialized with {algorithm}")
    
    def create_environment(self, data: np.ndarray, initial_balance: float = 10000.0):
        """Create trading environment."""
        self.env = TradingEnvironment(data, initial_balance)
        logger.info(f"✅ Trading environment created with {len(data)} steps")
    
    def train(self, 
              data: np.ndarray,
              total_timesteps: int = 100000,
              initial_balance: float = 10000.0) -> Dict[str, Any]:
        """Train RL agent."""
        
        if not RL_AVAILABLE:
            logger.warning("RL not available! Cannot train agent.")
            return {"mean_reward": 0.0, "win_rate": 0.0}
        
        logger.info(f"🚀 Training RL agent for 80% win rate...")
        logger.info(f"Algorithm: {self.algorithm}, Timesteps: {total_timesteps}")
        
        # Create environment
        self.create_environment(data, initial_balance)
        
        # Initialize agent
        if self.algorithm == "PPO":
            self.agent = PPO(
                "MlpPolicy",
                self.env,
                verbose=1,
                learning_rate=3e-4,
                n_steps=2048,
                batch_size=64,
                n_epochs=10,
                gamma=0.99,
                gae_lambda=0.95,
                clip_range=0.2,
                ent_coef=0.01
            )
        elif self.algorithm == "A2C":
            self.agent = A2C(
                "MlpPolicy",
                self.env,
                verbose=1,
                learning_rate=7e-4,
                n_steps=5,
                gamma=0.99,
                gae_lambda=1.0,
                ent_coef=0.01
            )
        elif self.algorithm == "DQN":
            self.agent = DQN(
                "MlpPolicy",
                self.env,
                verbose=1,
                learning_rate=1e-3,
                buffer_size=50000,
                learning_starts=1000,
                batch_size=32,
                tau=1.0,
                gamma=0.99,
                train_freq=4,
                gradient_steps=1,
                exploration_fraction=0.1,
                exploration_initial_eps=1.0,
                exploration_final_eps=0.05
            )
        else:
            raise ValueError(f"Unknown algorithm: {self.algorithm}")
        
        # Train agent
        self.agent.learn(total_timesteps=total_timesteps)
        
        self.is_trained = True
        
        # Evaluate performance
        results = self.evaluate(data, episodes=10)
        
        logger.info(f"✅ RL training completed!")
        logger.info(f"Mean reward: {results['mean_reward']:.4f}")
        logger.info(f"Win rate: {results['win_rate']:.1f}%")
        
        return results
    
    def predict(self, observation: np.ndarray) -> Tuple[int, float]:
        """Predict action and confidence."""
        
        if not RL_AVAILABLE or self.agent is None:
            # Random action as fallback
            action = np.random.randint(0, 3)
            confidence = 33.33
            return action, confidence
        
        if not self.is_trained:
            logger.warning("RL agent not trained! Using random action.")
            action = np.random.randint(0, 3)
            confidence = 33.33
            return action, confidence
        
        # Get action from agent
        action, _states = self.agent.predict(observation, deterministic=True)
        
        # Calculate confidence (simplified)
        # In practice, you might use the action probabilities
        confidence = 75.0  # Assume high confidence for trained agent
        
        return int(action), confidence
    
    def get_trading_signal(self, features: np.ndarray, portfolio_state: np.ndarray) -> Tuple[str, float]:
        """Get trading signal from RL agent."""
        
        # Combine features and portfolio state
        observation = np.concatenate([features, portfolio_state]).astype(np.float32)
        
        # Get action
        action, confidence = self.predict(observation)
        
        # Convert action to signal
        if action == 0:
            signal = "hold"
        elif action == 1:
            signal = "buy"
        else:
            signal = "sell"
        
        return signal, confidence
    
    def evaluate(self, data: np.ndarray, episodes: int = 10) -> Dict[str, float]:
        """Evaluate agent performance."""
        
        if not RL_AVAILABLE or self.agent is None or not self.is_trained:
            return {"mean_reward": 0.0, "win_rate": 0.0, "total_profit": 0.0}
        
        # Create evaluation environment
        eval_env = TradingEnvironment(data)
        
        total_rewards = []
        win_rates = []
        total_profits = []
        
        for episode in range(episodes):
            obs, _ = eval_env.reset()
            episode_reward = 0
            done = False
            
            while not done:
                action, _ = self.agent.predict(obs, deterministic=True)
                obs, reward, done, _, info = eval_env.step(action)
                episode_reward += reward
            
            total_rewards.append(episode_reward)
            win_rates.append(info["win_rate"])
            total_profits.append(info["total_profit"])
        
        results = {
            "mean_reward": np.mean(total_rewards),
            "std_reward": np.std(total_rewards),
            "win_rate": np.mean(win_rates),
            "total_profit": np.mean(total_profits)
        }
        
        return results
    
    def save_agent(self, filepath: str):
        """Save trained agent."""
        if self.agent is not None and RL_AVAILABLE:
            self.agent.save(filepath)
            logger.info(f"RL agent saved to {filepath}")
        else:
            logger.warning("No agent to save!")
    
    def load_agent(self, filepath: str):
        """Load trained agent."""
        if RL_AVAILABLE and os.path.exists(filepath):
            if self.algorithm == "PPO":
                self.agent = PPO.load(filepath)
            elif self.algorithm == "A2C":
                self.agent = A2C.load(filepath)
            elif self.algorithm == "DQN":
                self.agent = DQN.load(filepath)
            
            self.is_trained = True
            logger.info(f"RL agent loaded from {filepath}")
        else:
            logger.warning(f"Cannot load agent from {filepath}")


__all__ = ["RLTradingAgent", "TradingEnvironment"]
