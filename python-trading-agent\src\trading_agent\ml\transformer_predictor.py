"""
Transformer Model for Time Series Forecasting
============================================

🚀 ZACHRANA LUDSTVA - CUTTING-EDGE AI! 🚀
State-of-the-art Transformer with Attention Mechanism
"""

import numpy as np
import pandas as pd
from typing import Tuple, List, Optional, Dict, Any
import os

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

from ..utils.logger import get_logger

logger = get_logger(__name__)


if TORCH_AVAILABLE:
    class PositionalEncoding(nn.Module):
        """Positional encoding for transformer."""

        def __init__(self, d_model: int, max_len: int = 5000):
            super(PositionalEncoding, self).__init__()

            pe = torch.zeros(max_len, d_model)
            position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
            div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))

            pe[:, 0::2] = torch.sin(position * div_term)
            pe[:, 1::2] = torch.cos(position * div_term)
            pe = pe.unsqueeze(0).transpose(0, 1)

            self.register_buffer('pe', pe)

        def forward(self, x):
            return x + self.pe[:x.size(0), :]

    class TransformerTimeSeriesModel(nn.Module):
        """Transformer model for time series forecasting."""

        def __init__(self,
                     input_dim: int,
                     d_model: int = 128,
                     nhead: int = 8,
                     num_layers: int = 6,
                     dim_feedforward: int = 512,
                     dropout: float = 0.1,
                     output_dim: int = 1):

            super(TransformerTimeSeriesModel, self).__init__()

            self.d_model = d_model
            self.input_projection = nn.Linear(input_dim, d_model)
            self.pos_encoder = PositionalEncoding(d_model)

            encoder_layers = nn.TransformerEncoderLayer(
                d_model=d_model,
                nhead=nhead,
                dim_feedforward=dim_feedforward,
                dropout=dropout,
                batch_first=True
            )

            self.transformer_encoder = nn.TransformerEncoder(
                encoder_layers,
                num_layers=num_layers
            )

            self.output_projection = nn.Sequential(
                nn.Linear(d_model, dim_feedforward // 2),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(dim_feedforward // 2, output_dim)
            )

        logger.info(f"🤖 Transformer model initialized: {d_model}d, {nhead}h, {num_layers}L")
    
    def forward(self, src, src_mask=None):
        # Input projection
        src = self.input_projection(src) * np.sqrt(self.d_model)
        src = self.pos_encoder(src)
        
        # Transformer encoding
        output = self.transformer_encoder(src, src_mask)
        
        # Take the last time step for prediction
        output = output[:, -1, :]  # [batch_size, d_model]
        
        # Output projection
        output = self.output_projection(output)
        
        return output

else:
    # Dummy classes when PyTorch is not available
    class PositionalEncoding:
        def __init__(self, *args, **kwargs):
            pass

    class TransformerTimeSeriesModel:
        def __init__(self, *args, **kwargs):
            pass


class TransformerPredictor:
    """Transformer-based predictor for 80% win rate trading."""
    
    def __init__(self, 
                 sequence_length: int = 60,
                 prediction_horizon: int = 1,
                 d_model: int = 128,
                 nhead: int = 8,
                 num_layers: int = 6):
        
        if not TORCH_AVAILABLE:
            logger.warning("PyTorch not available! Using dummy predictor.")
            self.model = None
            return
        
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.d_model = d_model
        self.nhead = nhead
        self.num_layers = num_layers
        
        self.model = None
        self.is_trained = False
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        logger.info(f"🚀 Transformer Predictor initialized for 80% win rate!")
        logger.info(f"Device: {self.device}, Sequence: {sequence_length}")
    
    def prepare_sequences(self, data: np.ndarray, target: np.ndarray = None) -> Tuple[Any, Any]:
        """Prepare sequences for transformer training/prediction."""
        
        X, y = [], []
        
        for i in range(self.sequence_length, len(data)):
            X.append(data[i-self.sequence_length:i])
            if target is not None:
                if i + self.prediction_horizon <= len(target):
                    y.append(target[i:i+self.prediction_horizon])
        
        if TORCH_AVAILABLE:
            X = torch.FloatTensor(np.array(X))
            y = torch.FloatTensor(np.array(y)) if target is not None else None
        else:
            X = np.array(X)
            y = np.array(y) if target is not None else None
        
        logger.info(f"Prepared sequences: X {X.shape}, y {y.shape if y is not None else 'None'}")
        return X, y
    
    def train(self, 
              X: np.ndarray, 
              y: np.ndarray,
              validation_split: float = 0.2,
              epochs: int = 100,
              batch_size: int = 32,
              learning_rate: float = 0.001) -> Dict[str, float]:
        """Train transformer model."""
        
        if not TORCH_AVAILABLE:
            logger.warning("PyTorch not available! Cannot train Transformer.")
            return {"loss": 0.0, "val_loss": 0.0}
        
        logger.info(f"🚀 Training Transformer for 80% win rate...")
        logger.info(f"Training data: X {X.shape}, y {y.shape}")
        
        # Prepare sequences
        X_seq, y_seq = self.prepare_sequences(X, y)
        
        if len(X_seq) == 0:
            logger.error("No sequences prepared! Check data length.")
            return {"loss": float('inf'), "val_loss": float('inf')}
        
        # Split data
        split_idx = int(len(X_seq) * (1 - validation_split))
        X_train, X_val = X_seq[:split_idx], X_seq[split_idx:]
        y_train, y_val = y_seq[:split_idx], y_seq[split_idx:]
        
        # Initialize model
        input_dim = X_seq.shape[-1]
        self.model = TransformerTimeSeriesModel(
            input_dim=input_dim,
            d_model=self.d_model,
            nhead=self.nhead,
            num_layers=self.num_layers,
            output_dim=self.prediction_horizon
        ).to(self.device)
        
        # Loss and optimizer
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # Training loop
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(epochs):
            # Training
            self.model.train()
            train_loss = 0.0
            
            for i in range(0, len(X_train), batch_size):
                batch_X = X_train[i:i+batch_size].to(self.device)
                batch_y = y_train[i:i+batch_size].to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            # Validation
            self.model.eval()
            val_loss = 0.0
            
            with torch.no_grad():
                for i in range(0, len(X_val), batch_size):
                    batch_X = X_val[i:i+batch_size].to(self.device)
                    batch_y = y_val[i:i+batch_size].to(self.device)
                    
                    outputs = self.model(batch_X)
                    loss = criterion(outputs, batch_y)
                    val_loss += loss.item()
            
            train_loss /= len(X_train) // batch_size
            val_loss /= len(X_val) // batch_size
            
            scheduler.step(val_loss)
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= 15:
                    logger.info(f"Early stopping at epoch {epoch}")
                    break
            
            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
        
        self.is_trained = True
        
        logger.info(f"✅ Transformer training completed!")
        logger.info(f"Best validation loss: {best_val_loss:.6f}")
        
        return {
            "loss": train_loss,
            "val_loss": best_val_loss
        }
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using trained transformer."""
        
        if not TORCH_AVAILABLE or self.model is None:
            logger.warning("Transformer not available! Returning dummy predictions.")
            return np.random.randn(len(X), self.prediction_horizon) * 0.001
        
        if not self.is_trained:
            logger.warning("Transformer not trained! Returning dummy predictions.")
            return np.random.randn(len(X), self.prediction_horizon) * 0.001
        
        # Prepare sequences
        X_seq, _ = self.prepare_sequences(X)
        
        if len(X_seq) == 0:
            logger.warning("No sequences for prediction! Returning zeros.")
            return np.zeros((1, self.prediction_horizon))
        
        # Make predictions
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for i in range(0, len(X_seq), 32):  # Batch size 32
                batch_X = X_seq[i:i+32].to(self.device)
                outputs = self.model(batch_X)
                predictions.append(outputs.cpu().numpy())
        
        predictions = np.vstack(predictions)
        
        logger.info(f"Transformer predictions: {predictions.shape}")
        return predictions
    
    def get_prediction_confidence(self, recent_data: np.ndarray) -> float:
        """Get confidence score for prediction (0-100)."""
        
        if not TORCH_AVAILABLE or self.model is None or not self.is_trained:
            return 50.0  # Neutral confidence
        
        try:
            # Make multiple predictions with dropout (Monte Carlo)
            self.model.train()  # Enable dropout
            predictions = []
            
            for _ in range(10):
                pred = self.predict_next_price(recent_data)
                predictions.append(pred)
            
            # Calculate confidence based on prediction variance
            pred_std = np.std(predictions)
            pred_mean = np.mean(predictions)
            
            # Lower variance = higher confidence
            if pred_std == 0:
                confidence = 95.0
            else:
                # Normalize confidence (this is a heuristic)
                confidence = max(60.0, min(95.0, 85.0 - (pred_std / abs(pred_mean + 1e-8)) * 1000))
            
            return confidence
            
        except Exception as e:
            logger.error(f"Error calculating transformer confidence: {e}")
            return 50.0
    
    def predict_next_price(self, recent_data: np.ndarray) -> float:
        """Predict next price movement."""
        
        if len(recent_data) < self.sequence_length:
            logger.warning(f"Not enough data for prediction. Need {self.sequence_length}, got {len(recent_data)}")
            return 0.0
        
        # Use last sequence_length points
        X = recent_data[-self.sequence_length:].reshape(1, -1)
        
        # Predict
        prediction = self.predict(X)
        
        if len(prediction) > 0:
            return float(prediction[0, 0])  # Return first prediction
        else:
            return 0.0


__all__ = ["TransformerPredictor"]
