"""
Trading Data Models
==================

Pydantic models for type-safe trading operations.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator


class TradeAction(str, Enum):
    """Trade action types."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


class RiskLevel(str, Enum):
    """Risk level classifications."""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class SignalQuality(str, Enum):
    """Signal quality grades."""
    A_PLUS = "A+"
    A = "A"
    B = "B"
    C = "C"
    D = "D"
    F = "F"


class MarketSession(str, Enum):
    """Trading session types."""
    ASIAN = "asian"
    LONDON = "london"
    NEW_YORK = "newyork"
    OVERLAP = "overlap"
    DEAD = "dead"


class Recommendation(str, Enum):
    """Trading recommendations."""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"


class MarketData(BaseModel):
    """Market data point."""
    symbol: str
    timestamp: datetime
    open: float = Field(gt=0)
    high: float = Field(gt=0)
    low: float = Field(gt=0)
    close: float = Field(gt=0)
    volume: Optional[float] = Field(default=None, ge=0)
    
    @field_validator('high')
    @classmethod
    def validate_high(cls, v, info):
        if info.data and 'low' in info.data and v < info.data['low']:
            raise ValueError('High must be >= low')
        return v

    @field_validator('close')
    @classmethod
    def validate_close(cls, v, info):
        if info.data and 'low' in info.data and 'high' in info.data:
            if not (info.data['low'] <= v <= info.data['high']):
                raise ValueError('Close must be between low and high')
        return v


class TradeOrder(BaseModel):
    """Trade order model."""
    action: TradeAction
    symbol: str
    amount: float = Field(gt=0)
    price: Optional[float] = Field(default=None, gt=0)
    stop_loss: Optional[float] = Field(default=None, gt=0)
    take_profit: Optional[float] = Field(default=None, gt=0)
    timestamp: datetime = Field(default_factory=datetime.now)
    order_id: Optional[str] = None


class SignalStrength(BaseModel):
    """Signal strength indicators."""
    buy: float = Field(ge=0, le=100)
    sell: float = Field(ge=0, le=100)
    confidence: float = Field(ge=0, le=100)
    quality: SignalQuality
    confirmations: int = Field(ge=0)
    divergences: int = Field(ge=0)


class MarketCondition(BaseModel):
    """Market condition analysis."""
    trend: Literal["bullish", "bearish", "sideways"]
    trend_strength: float = Field(ge=0, le=100)
    volatility: float = Field(ge=0, le=100)
    volume_profile: Literal["high", "medium", "low"]
    session: MarketSession
    is_major_news: bool = False


class ICTSetup(BaseModel):
    """ICT (Inner Circle Trader) setup analysis."""
    has_setup: bool
    setup_type: Optional[str] = None
    confidence: float = Field(ge=0, le=100)
    entry_zone: Optional[Dict[str, float]] = None
    invalidation_level: Optional[float] = None


class EntryTiming(BaseModel):
    """Entry timing analysis."""
    is_optimal: bool
    score: float = Field(ge=0, le=100)
    factors: List[str] = Field(default_factory=list)
    wait_for: Optional[str] = None


class TradingAnalysis(BaseModel):
    """Complete trading analysis."""
    signals: SignalStrength
    market_condition: MarketCondition
    risk_level: RiskLevel
    recommendation: Recommendation
    reasoning: str
    ict_setup: Optional[ICTSetup] = None
    entry_timing: Optional[EntryTiming] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class Portfolio(BaseModel):
    """Portfolio state."""
    balance: float = Field(ge=0)
    positions: Dict[str, float] = Field(default_factory=dict)
    total_trades: int = Field(ge=0, default=0)
    winning_trades: int = Field(ge=0, default=0)
    losing_trades: int = Field(ge=0, default=0)
    total_profit_loss: float = Field(default=0.0)
    max_drawdown: float = Field(le=0, default=0.0)
    
    @property
    def win_rate(self) -> float:
        """Calculate win rate percentage."""
        if self.total_trades == 0:
            return 0.0
        return (self.winning_trades / self.total_trades) * 100
    
    @property
    def profit_factor(self) -> float:
        """Calculate profit factor."""
        if self.losing_trades == 0:
            return float('inf') if self.winning_trades > 0 else 0.0
        
        total_wins = sum(p for p in self.positions.values() if p > 0)
        total_losses = abs(sum(p for p in self.positions.values() if p < 0))
        
        return total_wins / total_losses if total_losses > 0 else 0.0


class BacktestResult(BaseModel):
    """Backtesting result."""
    strategy_name: str
    symbol: str
    start_date: datetime
    end_date: datetime
    initial_balance: float
    final_balance: float
    total_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    avg_trade_duration: float  # in minutes
    best_trade: float
    worst_trade: float


class StrategyConfig(BaseModel):
    """Strategy configuration."""
    name: str
    min_confidence: float = Field(ge=0, le=100, default=70.0)
    min_signal_strength: float = Field(ge=0, le=100, default=60.0)
    min_confirmations: int = Field(ge=0, default=2)
    max_divergences: int = Field(ge=0, default=3)
    max_risk_level: RiskLevel = RiskLevel.MEDIUM
    max_consecutive_losses: int = Field(ge=1, default=3)
    max_daily_trades: int = Field(ge=1, default=10)
    min_time_between_trades: int = Field(ge=60000, default=300000)  # milliseconds
    required_trend_strength: float = Field(ge=0, le=100, default=60.0)
    avoid_high_volatility: bool = True
    require_volume_confirmation: bool = True
    require_ict_setup: bool = False
    min_ict_confidence: float = Field(ge=0, le=100, default=70.0)
    preferred_sessions: List[MarketSession] = Field(
        default_factory=lambda: [MarketSession.LONDON, MarketSession.NEW_YORK]
    )
    avoid_dead_hours: bool = True
    min_quality: SignalQuality = SignalQuality.C


__all__ = [
    "TradeAction",
    "RiskLevel", 
    "SignalQuality",
    "MarketSession",
    "Recommendation",
    "MarketData",
    "TradeOrder",
    "SignalStrength",
    "MarketCondition",
    "ICTSetup",
    "EntryTiming", 
    "TradingAnalysis",
    "Portfolio",
    "BacktestResult",
    "StrategyConfig",
]
