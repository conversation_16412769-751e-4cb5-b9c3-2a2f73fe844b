"""
Base Trading Strategy
====================

Abstract base class for all trading strategies.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional

import numpy as np
import pandas as pd
# import talib  # Optional - will use manual calculations

from ..models.types import (
    MarketData, 
    TradeOrder, 
    TradingAnalysis,
    StrategyConfig,
    SignalStrength,
    MarketCondition,
    RiskLevel,
    Recommendation,
    SignalQuality,
    MarketSession
)
from ..utils.config import config
from ..utils.logger import get_logger, log_signal

logger = get_logger(__name__)


class BaseStrategy(ABC):
    """Abstract base class for trading strategies."""
    
    def __init__(self, strategy_config: Optional[StrategyConfig] = None):
        self.config = strategy_config or StrategyConfig(name=self.__class__.__name__)
        self.price_history: List[float] = []
        self.volume_history: List[float] = []
        self.analysis_history: List[TradingAnalysis] = []
        self.last_trade_time: Optional[datetime] = None
        self.consecutive_losses = 0
        self.daily_trades = 0
        
        logger.info(f"Strategy {self.config.name} initialized")
    
    @abstractmethod
    async def analyze(self, market_data: List[MarketData]) -> TradingAnalysis:
        """Analyze market data and return trading analysis."""
        pass
    
    @abstractmethod
    async def decide_action(self, analysis: TradingAnalysis) -> Optional[TradeOrder]:
        """Make trading decision based on analysis."""
        pass
    
    def update_history(self, market_data: MarketData):
        """Update price and volume history."""
        self.price_history.append(market_data.close)
        if market_data.volume:
            self.volume_history.append(market_data.volume)
        
        # Keep only last 1000 data points for performance
        if len(self.price_history) > 1000:
            self.price_history = self.price_history[-1000:]
        if len(self.volume_history) > 1000:
            self.volume_history = self.volume_history[-1000:]
    
    def calculate_technical_indicators(self) -> Dict[str, float]:
        """Calculate common technical indicators using manual calculations."""
        if len(self.price_history) < 20:
            return {}

        prices = np.array(self.price_history)

        indicators = {}

        try:
            # Moving Averages
            indicators['sma_20'] = np.mean(prices[-20:])
            indicators['sma_50'] = np.mean(prices[-50:]) if len(prices) >= 50 else 0
            indicators['ema_12'] = self._calculate_ema(prices, 12)
            indicators['ema_26'] = self._calculate_ema(prices, 26) if len(prices) >= 26 else 0

            # RSI
            indicators['rsi'] = self._calculate_rsi(prices, 14) if len(prices) >= 14 else 50

            # MACD
            if len(prices) >= 26:
                ema_12 = self._calculate_ema(prices, 12)
                ema_26 = self._calculate_ema(prices, 26)
                macd = ema_12 - ema_26
                indicators['macd'] = macd
                indicators['macd_signal'] = macd * 0.9  # Simplified signal line
                indicators['macd_histogram'] = macd - indicators['macd_signal']

            # Bollinger Bands
            if len(prices) >= 20:
                sma_20 = indicators['sma_20']
                std_20 = np.std(prices[-20:])
                indicators['bb_upper'] = sma_20 + (2 * std_20)
                indicators['bb_middle'] = sma_20
                indicators['bb_lower'] = sma_20 - (2 * std_20)
                indicators['bb_position'] = (prices[-1] - indicators['bb_lower']) / (indicators['bb_upper'] - indicators['bb_lower'])

            # Stochastic (simplified)
            if len(prices) >= 14:
                high_14 = np.max(prices[-14:])
                low_14 = np.min(prices[-14:])
                if high_14 != low_14:
                    indicators['stoch_k'] = ((prices[-1] - low_14) / (high_14 - low_14)) * 100
                    indicators['stoch_d'] = indicators['stoch_k'] * 0.9  # Simplified
                else:
                    indicators['stoch_k'] = 50
                    indicators['stoch_d'] = 50

            # ATR (simplified)
            if len(prices) >= 14:
                price_changes = np.abs(np.diff(prices[-14:]))
                indicators['atr'] = np.mean(price_changes)

            # Volume indicators (if available)
            if len(self.volume_history) >= 20:
                volumes = np.array(self.volume_history)
                indicators['volume_sma'] = np.mean(volumes[-20:])
                indicators['volume_ratio'] = volumes[-1] / indicators['volume_sma'] if indicators['volume_sma'] > 0 else 1

        except Exception as e:
            logger.warning(f"Error calculating technical indicators: {e}")

        return indicators

    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """Calculate Exponential Moving Average."""
        if len(prices) < period:
            return np.mean(prices)

        multiplier = 2 / (period + 1)
        ema = prices[0]

        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """Calculate Relative Strength Index."""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi
    
    def calculate_signal_strength(self, indicators: Dict[str, float]) -> SignalStrength:
        """Calculate signal strength from technical indicators."""
        buy_signals = 0
        sell_signals = 0
        total_signals = 0
        confirmations = 0
        
        current_price = self.price_history[-1] if self.price_history else 0
        
        # RSI signals
        if 'rsi' in indicators:
            total_signals += 1
            if indicators['rsi'] < 30:
                buy_signals += 1
                confirmations += 1
            elif indicators['rsi'] > 70:
                sell_signals += 1
                confirmations += 1
        
        # MACD signals
        if 'macd' in indicators and 'macd_signal' in indicators:
            total_signals += 1
            if indicators['macd'] > indicators['macd_signal']:
                buy_signals += 1
            else:
                sell_signals += 1
        
        # Moving Average signals
        if 'sma_20' in indicators and 'sma_50' in indicators and indicators['sma_50'] > 0:
            total_signals += 1
            if indicators['sma_20'] > indicators['sma_50']:
                buy_signals += 1
            else:
                sell_signals += 1
        
        # Bollinger Bands signals
        if 'bb_position' in indicators:
            total_signals += 1
            if indicators['bb_position'] < 0.2:
                buy_signals += 1
                confirmations += 1
            elif indicators['bb_position'] > 0.8:
                sell_signals += 1
                confirmations += 1
        
        # Stochastic signals
        if 'stoch_k' in indicators and 'stoch_d' in indicators:
            total_signals += 1
            if indicators['stoch_k'] < 20 and indicators['stoch_k'] > indicators['stoch_d']:
                buy_signals += 1
            elif indicators['stoch_k'] > 80 and indicators['stoch_k'] < indicators['stoch_d']:
                sell_signals += 1
        
        # Calculate percentages
        buy_percentage = (buy_signals / total_signals * 100) if total_signals > 0 else 0
        sell_percentage = (sell_signals / total_signals * 100) if total_signals > 0 else 0
        
        # Calculate confidence
        confidence = max(buy_percentage, sell_percentage)
        
        # Determine quality
        quality = self._determine_signal_quality(confidence, confirmations, total_signals)
        
        return SignalStrength(
            buy=buy_percentage,
            sell=sell_percentage,
            confidence=confidence,
            quality=quality,
            confirmations=confirmations,
            divergences=0  # Simplified for now
        )
    
    def _determine_signal_quality(self, confidence: float, confirmations: int, total_signals: int) -> SignalQuality:
        """Determine signal quality grade."""
        if confidence >= 90 and confirmations >= 3:
            return SignalQuality.A_PLUS
        elif confidence >= 80 and confirmations >= 2:
            return SignalQuality.A
        elif confidence >= 70 and confirmations >= 1:
            return SignalQuality.B
        elif confidence >= 60:
            return SignalQuality.C
        elif confidence >= 50:
            return SignalQuality.D
        else:
            return SignalQuality.F
    
    def analyze_market_condition(self, indicators: Dict[str, float]) -> MarketCondition:
        """Analyze current market condition."""
        # Determine trend
        trend = "sideways"
        trend_strength = 50.0
        
        if 'sma_20' in indicators and 'sma_50' in indicators and indicators['sma_50'] > 0:
            if indicators['sma_20'] > indicators['sma_50'] * 1.01:
                trend = "bullish"
                trend_strength = min(((indicators['sma_20'] / indicators['sma_50']) - 1) * 1000, 100)
            elif indicators['sma_20'] < indicators['sma_50'] * 0.99:
                trend = "bearish"
                trend_strength = min(((indicators['sma_50'] / indicators['sma_20']) - 1) * 1000, 100)
        
        # Calculate volatility
        volatility = 50.0
        if 'atr' in indicators and self.price_history:
            current_price = self.price_history[-1]
            volatility = min((indicators['atr'] / current_price) * 1000, 100)
        
        # Volume profile
        volume_profile = "medium"
        if 'volume_ratio' in indicators:
            if indicators['volume_ratio'] > 1.5:
                volume_profile = "high"
            elif indicators['volume_ratio'] < 0.5:
                volume_profile = "low"
        
        # Market session (simplified)
        current_hour = datetime.now().hour
        if 0 <= current_hour < 8:
            session = MarketSession.ASIAN
        elif 8 <= current_hour < 16:
            session = MarketSession.LONDON
        elif 16 <= current_hour < 24:
            session = MarketSession.NEW_YORK
        else:
            session = MarketSession.DEAD
        
        return MarketCondition(
            trend=trend,
            trend_strength=trend_strength,
            volatility=volatility,
            volume_profile=volume_profile,
            session=session,
            is_major_news=False  # Would need news feed integration
        )
    
    def assess_risk_level(self, market_condition: MarketCondition, signals: SignalStrength) -> RiskLevel:
        """Assess current risk level."""
        risk_score = 0
        
        # Volatility risk
        if market_condition.volatility > 80:
            risk_score += 3
        elif market_condition.volatility > 60:
            risk_score += 2
        elif market_condition.volatility > 40:
            risk_score += 1
        
        # Signal quality risk
        if signals.quality in [SignalQuality.F, SignalQuality.D]:
            risk_score += 2
        elif signals.quality == SignalQuality.C:
            risk_score += 1
        
        # Confidence risk
        if signals.confidence < 60:
            risk_score += 2
        elif signals.confidence < 70:
            risk_score += 1
        
        # Session risk
        if market_condition.session == MarketSession.DEAD:
            risk_score += 1
        
        # Map score to risk level
        if risk_score >= 6:
            return RiskLevel.VERY_HIGH
        elif risk_score >= 4:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        elif risk_score >= 1:
            return RiskLevel.LOW
        else:
            return RiskLevel.VERY_LOW
    
    def generate_recommendation(
        self, 
        signals: SignalStrength, 
        market_condition: MarketCondition, 
        risk_level: RiskLevel
    ) -> Recommendation:
        """Generate trading recommendation."""
        # Don't trade in high risk conditions
        if risk_level in [RiskLevel.VERY_HIGH, RiskLevel.HIGH]:
            return Recommendation.HOLD
        
        # Don't trade with low confidence
        if signals.confidence < self.config.min_confidence:
            return Recommendation.HOLD
        
        # Generate recommendation based on signals
        if signals.buy > signals.sell and signals.buy >= self.config.min_signal_strength:
            if signals.confidence >= 85 and signals.quality in [SignalQuality.A_PLUS, SignalQuality.A]:
                return Recommendation.STRONG_BUY
            else:
                return Recommendation.BUY
        elif signals.sell > signals.buy and signals.sell >= self.config.min_signal_strength:
            if signals.confidence >= 85 and signals.quality in [SignalQuality.A_PLUS, SignalQuality.A]:
                return Recommendation.STRONG_SELL
            else:
                return Recommendation.SELL
        else:
            return Recommendation.HOLD


__all__ = ["BaseStrategy"]
