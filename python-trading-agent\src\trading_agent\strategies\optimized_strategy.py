"""
Optimized Trading Strategy
=========================

High-performance optimized trading strategy with advanced features.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any

from .base_strategy import BaseStrategy
from ..models.types import (
    MarketData,
    TradeOrder,
    TradingAnalysis,
    TradeAction,
    StrategyConfig,
    ICTSetup,
    EntryTiming,
    SignalQuality,
    RiskLevel
)
from ..utils.config import config
from ..utils.logger import get_logger, log_signal

logger = get_logger(__name__)


class OptimizedStrategy(BaseStrategy):
    """Optimized trading strategy with ICT concepts and advanced timing."""
    
    def __init__(self, strategy_config: Optional[StrategyConfig] = None):
        if strategy_config is None:
            strategy_config = StrategyConfig(
                name="OptimizedStrategy",
                min_confidence=75.0,
                min_signal_strength=65.0,
                min_confirmations=3,
                max_divergences=2,
                required_trend_strength=70.0,
                avoid_high_volatility=True,
                require_volume_confirmation=True,
                require_ict_setup=True,
                min_ict_confidence=70.0,
                min_quality=SignalQuality.B
            )
        
        super().__init__(strategy_config)
        
        # Additional optimization parameters
        self.optimization_config = {
            "use_machine_learning": False,  # Future enhancement
            "dynamic_position_sizing": True,
            "advanced_risk_management": True,
            "multi_timeframe_analysis": True,
            "sentiment_analysis": False,  # Future enhancement
            "news_integration": False,    # Future enhancement
        }
        
        logger.info(f"OptimizedStrategy initialized with advanced features")
    
    async def analyze(self, market_data: List[MarketData]) -> TradingAnalysis:
        """Perform optimized analysis with advanced features."""
        if not market_data:
            raise ValueError("No market data provided")
        
        # Update history with latest data
        for data_point in market_data[-200:]:  # Use more data points for optimization
            self.update_history(data_point)
        
        if len(self.price_history) < 50:  # Higher minimum for optimization
            return self._create_hold_analysis("Insufficient data for optimized analysis")
        
        # Multi-timeframe analysis
        indicators = self._calculate_multi_timeframe_indicators()
        
        # Enhanced signal calculation
        signals = self._calculate_enhanced_signals(indicators)
        
        # Advanced market condition analysis
        market_condition = self._analyze_advanced_market_condition(indicators)
        
        # ICT setup analysis
        ict_setup = self._analyze_ict_setup(indicators)
        
        # Entry timing analysis
        entry_timing = self._analyze_entry_timing(signals, market_condition, ict_setup)
        
        # Advanced risk assessment
        risk_level = self._assess_advanced_risk(market_condition, signals, ict_setup)
        
        # Optimized recommendation
        recommendation = self._generate_optimized_recommendation(
            signals, market_condition, risk_level, ict_setup, entry_timing
        )
        
        # Enhanced reasoning
        reasoning = self._generate_enhanced_reasoning(
            signals, market_condition, risk_level, ict_setup, entry_timing, indicators
        )
        
        analysis = TradingAnalysis(
            signals=signals,
            market_condition=market_condition,
            risk_level=risk_level,
            recommendation=recommendation,
            reasoning=reasoning,
            ict_setup=ict_setup,
            entry_timing=entry_timing
        )
        
        # Log the analysis
        log_signal(
            strategy=self.config.name,
            signal=recommendation.value,
            confidence=signals.confidence,
            symbol=market_data[-1].symbol
        )
        
        self.analysis_history.append(analysis)
        
        return analysis
    
    async def decide_action(self, analysis: TradingAnalysis) -> Optional[TradeOrder]:
        """Make optimized trading decision."""
        # Enhanced trade validation
        if not self._should_trade_optimized(analysis):
            return None
        
        # Determine action with ICT consideration
        if analysis.recommendation.value in ["strong_buy", "buy"]:
            action = TradeAction.BUY
        elif analysis.recommendation.value in ["strong_sell", "sell"]:
            action = TradeAction.SELL
        else:
            return None
        
        # Dynamic position sizing
        amount = self._calculate_dynamic_position_size(analysis)
        
        # Get current price
        current_price = self.price_history[-1] if self.price_history else 1.0
        
        # Advanced stop loss and take profit
        stop_loss, take_profit = self._calculate_advanced_levels(
            current_price, action, analysis
        )
        
        # Create optimized trade order
        trade_order = TradeOrder(
            action=action,
            symbol="EUR/USD",  # Default symbol
            amount=amount,
            price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        logger.info(
            f"OptimizedStrategy decision: {action.value} {amount} @ {current_price:.4f} "
            f"(confidence: {analysis.signals.confidence:.1f}%, "
            f"ICT: {analysis.ict_setup.has_setup if analysis.ict_setup else False})"
        )
        
        return trade_order
    
    def _calculate_multi_timeframe_indicators(self) -> Dict[str, Any]:
        """Calculate indicators across multiple timeframes."""
        indicators = self.calculate_technical_indicators()
        
        # Add multi-timeframe analysis
        if len(self.price_history) >= 100:
            # Short-term (last 20 periods)
            short_term = self.price_history[-20:]
            indicators['short_term_trend'] = self._calculate_trend_direction(short_term)
            
            # Medium-term (last 50 periods)
            medium_term = self.price_history[-50:]
            indicators['medium_term_trend'] = self._calculate_trend_direction(medium_term)
            
            # Long-term (last 100 periods)
            long_term = self.price_history[-100:]
            indicators['long_term_trend'] = self._calculate_trend_direction(long_term)
            
            # Trend alignment score
            trends = [
                indicators['short_term_trend'],
                indicators['medium_term_trend'],
                indicators['long_term_trend']
            ]
            indicators['trend_alignment'] = self._calculate_trend_alignment(trends)
        
        return indicators
    
    def _calculate_enhanced_signals(self, indicators: Dict[str, Any]) -> Any:
        """Calculate enhanced signal strength with optimization."""
        base_signals = self.calculate_signal_strength(indicators)
        
        # Enhancement factors
        enhancement_score = 0
        
        # Multi-timeframe alignment bonus
        if 'trend_alignment' in indicators:
            alignment = indicators['trend_alignment']
            if alignment >= 0.8:
                enhancement_score += 15
            elif alignment >= 0.6:
                enhancement_score += 10
            elif alignment >= 0.4:
                enhancement_score += 5
        
        # Volume confirmation bonus
        if 'volume_ratio' in indicators and indicators['volume_ratio'] > 1.2:
            enhancement_score += 10
        
        # Volatility optimization
        if 'atr' in indicators:
            atr_ratio = indicators['atr'] / self.price_history[-1] if self.price_history else 0
            if 0.001 <= atr_ratio <= 0.005:  # Optimal volatility range
                enhancement_score += 5
        
        # Apply enhancements
        enhanced_confidence = min(base_signals.confidence + enhancement_score, 100)
        
        # Upgrade quality if significantly enhanced
        enhanced_quality = base_signals.quality
        if enhancement_score >= 20:
            quality_upgrades = {
                "D": SignalQuality.C,
                "C": SignalQuality.B,
                "B": SignalQuality.A,
                "A": SignalQuality.A_PLUS
            }
            enhanced_quality = quality_upgrades.get(base_signals.quality.value, base_signals.quality)
        
        # Create enhanced signals
        from ..models.types import SignalStrength
        return SignalStrength(
            buy=base_signals.buy,
            sell=base_signals.sell,
            confidence=enhanced_confidence,
            quality=enhanced_quality,
            confirmations=base_signals.confirmations + (1 if enhancement_score >= 10 else 0),
            divergences=base_signals.divergences
        )
    
    def _analyze_advanced_market_condition(self, indicators: Dict[str, Any]) -> Any:
        """Analyze market condition with advanced features."""
        base_condition = self.analyze_market_condition(indicators)
        
        # Enhanced trend strength calculation
        enhanced_trend_strength = base_condition.trend_strength
        
        if 'trend_alignment' in indicators:
            alignment_bonus = indicators['trend_alignment'] * 20
            enhanced_trend_strength = min(enhanced_trend_strength + alignment_bonus, 100)
        
        # Create enhanced market condition
        from ..models.types import MarketCondition
        return MarketCondition(
            trend=base_condition.trend,
            trend_strength=enhanced_trend_strength,
            volatility=base_condition.volatility,
            volume_profile=base_condition.volume_profile,
            session=base_condition.session,
            is_major_news=base_condition.is_major_news
        )
    
    def _analyze_ict_setup(self, indicators: Dict[str, Any]) -> ICTSetup:
        """Analyze ICT (Inner Circle Trader) setup."""
        has_setup = False
        setup_type = None
        confidence = 0.0
        entry_zone = None
        invalidation_level = None
        
        if len(self.price_history) >= 50:
            current_price = self.price_history[-1]
            
            # Look for order blocks (simplified)
            recent_highs = self._find_recent_highs()
            recent_lows = self._find_recent_lows()
            
            # Fair Value Gap detection (simplified)
            if self._detect_fair_value_gap():
                has_setup = True
                setup_type = "Fair Value Gap"
                confidence = 75.0
                entry_zone = {"low": current_price * 0.999, "high": current_price * 1.001}
            
            # Liquidity sweep detection
            elif self._detect_liquidity_sweep(recent_highs, recent_lows):
                has_setup = True
                setup_type = "Liquidity Sweep"
                confidence = 80.0
                entry_zone = {"low": current_price * 0.998, "high": current_price * 1.002}
            
            # Order block detection
            elif self._detect_order_block():
                has_setup = True
                setup_type = "Order Block"
                confidence = 70.0
                entry_zone = {"low": current_price * 0.9995, "high": current_price * 1.0005}
            
            # Set invalidation level
            if has_setup:
                if setup_type in ["Fair Value Gap", "Order Block"]:
                    invalidation_level = current_price * 0.995  # 0.5% invalidation
                else:
                    invalidation_level = current_price * 0.99   # 1% invalidation
        
        return ICTSetup(
            has_setup=has_setup,
            setup_type=setup_type,
            confidence=confidence,
            entry_zone=entry_zone,
            invalidation_level=invalidation_level
        )
    
    def _analyze_entry_timing(self, signals, market_condition, ict_setup: ICTSetup) -> EntryTiming:
        """Analyze optimal entry timing."""
        timing_score = 0
        factors = []
        wait_for = None
        
        # Signal timing
        if signals.confidence >= 80:
            timing_score += 30
            factors.append("High confidence signals")
        elif signals.confidence >= 70:
            timing_score += 20
            factors.append("Good confidence signals")
        
        # Market condition timing
        if market_condition.trend_strength >= 70:
            timing_score += 25
            factors.append("Strong trend")
        
        if market_condition.volatility <= 60:
            timing_score += 15
            factors.append("Moderate volatility")
        elif market_condition.volatility >= 80:
            timing_score -= 20
            factors.append("High volatility risk")
        
        # ICT timing
        if ict_setup and ict_setup.has_setup:
            timing_score += 20
            factors.append(f"ICT setup: {ict_setup.setup_type}")
        else:
            wait_for = "ICT setup formation"
        
        # Session timing
        if market_condition.session.value in ["london", "newyork"]:
            timing_score += 10
            factors.append("Active trading session")
        elif market_condition.session.value == "overlap":
            timing_score += 15
            factors.append("Session overlap")
        
        is_optimal = timing_score >= 70
        
        return EntryTiming(
            is_optimal=is_optimal,
            score=min(timing_score, 100),
            factors=factors,
            wait_for=wait_for
        )
    
    def _should_trade_optimized(self, analysis: TradingAnalysis) -> bool:
        """Enhanced trade validation for optimized strategy."""
        # Base validation
        if not self._should_trade(analysis):
            return False
        
        # ICT setup requirement
        if self.config.require_ict_setup:
            if not analysis.ict_setup or not analysis.ict_setup.has_setup:
                logger.debug("ICT setup required but not present")
                return False
            
            if analysis.ict_setup.confidence < self.config.min_ict_confidence:
                logger.debug(f"ICT confidence too low: {analysis.ict_setup.confidence}")
                return False
        
        # Entry timing requirement
        if analysis.entry_timing and not analysis.entry_timing.is_optimal:
            logger.debug(f"Entry timing not optimal: {analysis.entry_timing.score}")
            return False
        
        # Quality requirement
        quality_scores = {"A+": 5, "A": 4, "B": 3, "C": 2, "D": 1, "F": 0}
        min_quality_score = quality_scores.get(self.config.min_quality.value, 2)
        current_quality_score = quality_scores.get(analysis.signals.quality.value, 0)
        
        if current_quality_score < min_quality_score:
            logger.debug(f"Signal quality too low: {analysis.signals.quality.value}")
            return False
        
        return True
    
    def _calculate_dynamic_position_size(self, analysis: TradingAnalysis) -> float:
        """Calculate dynamic position size based on multiple factors."""
        base_size = 12.0  # Larger base size for optimized strategy
        
        # Confidence multiplier
        confidence_multiplier = analysis.signals.confidence / 100
        
        # Quality multiplier
        quality_multipliers = {
            "A+": 1.8,
            "A": 1.5,
            "B": 1.2,
            "C": 1.0,
            "D": 0.7,
            "F": 0.4
        }
        quality_multiplier = quality_multipliers.get(analysis.signals.quality.value, 1.0)
        
        # Risk multiplier
        risk_multipliers = {
            "very_low": 1.5,
            "low": 1.2,
            "medium": 1.0,
            "high": 0.6,
            "very_high": 0.3
        }
        risk_multiplier = risk_multipliers.get(analysis.risk_level.value, 1.0)
        
        # ICT multiplier
        ict_multiplier = 1.0
        if analysis.ict_setup and analysis.ict_setup.has_setup:
            ict_multiplier = 1.3
        
        # Trend alignment multiplier
        trend_multiplier = analysis.market_condition.trend_strength / 100
        
        # Entry timing multiplier
        timing_multiplier = 1.0
        if analysis.entry_timing:
            timing_multiplier = analysis.entry_timing.score / 100
        
        # Calculate final size
        final_size = (base_size * confidence_multiplier * quality_multiplier * 
                     risk_multiplier * ict_multiplier * trend_multiplier * timing_multiplier)
        
        # Ensure reasonable bounds
        return max(min(final_size, 50.0), 2.0)
    
    # Helper methods (simplified implementations)
    def _calculate_trend_direction(self, prices: List[float]) -> float:
        """Calculate trend direction (-1 to 1)."""
        if len(prices) < 2:
            return 0
        return 1 if prices[-1] > prices[0] else -1
    
    def _calculate_trend_alignment(self, trends: List[float]) -> float:
        """Calculate trend alignment score (0 to 1)."""
        if not trends:
            return 0
        positive_trends = sum(1 for t in trends if t > 0)
        negative_trends = sum(1 for t in trends if t < 0)
        return max(positive_trends, negative_trends) / len(trends)
    
    def _find_recent_highs(self) -> List[float]:
        """Find recent high points."""
        if len(self.price_history) < 10:
            return []
        return [max(self.price_history[-10:])]
    
    def _find_recent_lows(self) -> List[float]:
        """Find recent low points."""
        if len(self.price_history) < 10:
            return []
        return [min(self.price_history[-10:])]
    
    def _detect_fair_value_gap(self) -> bool:
        """Detect fair value gap (simplified)."""
        return len(self.price_history) >= 3 and abs(self.price_history[-1] - self.price_history[-3]) > 0.001
    
    def _detect_liquidity_sweep(self, highs: List[float], lows: List[float]) -> bool:
        """Detect liquidity sweep (simplified)."""
        if not highs or not lows or not self.price_history:
            return False
        current = self.price_history[-1]
        return current > max(highs) or current < min(lows)
    
    def _detect_order_block(self) -> bool:
        """Detect order block (simplified)."""
        return len(self.price_history) >= 5
    
    def _assess_advanced_risk(self, market_condition, signals, ict_setup: ICTSetup) -> RiskLevel:
        """Advanced risk assessment."""
        base_risk = self.assess_risk_level(market_condition, signals)
        
        # ICT setup reduces risk
        if ict_setup and ict_setup.has_setup and ict_setup.confidence >= 75:
            risk_levels = ["very_high", "high", "medium", "low", "very_low"]
            current_index = risk_levels.index(base_risk.value)
            if current_index > 0:
                improved_risk = risk_levels[current_index - 1]
                return RiskLevel(improved_risk)
        
        return base_risk
    
    def _generate_optimized_recommendation(self, signals, market_condition, risk_level, ict_setup, entry_timing):
        """Generate optimized recommendation."""
        base_recommendation = self.generate_recommendation(signals, market_condition, risk_level)
        
        # Upgrade recommendation if all factors align
        if (ict_setup and ict_setup.has_setup and 
            entry_timing and entry_timing.is_optimal and
            signals.quality.value in ["A+", "A"]):
            
            if base_recommendation.value == "buy":
                from ..models.types import Recommendation
                return Recommendation.STRONG_BUY
            elif base_recommendation.value == "sell":
                from ..models.types import Recommendation
                return Recommendation.STRONG_SELL
        
        return base_recommendation
    
    def _generate_enhanced_reasoning(self, signals, market_condition, risk_level, ict_setup, entry_timing, indicators) -> str:
        """Generate enhanced reasoning with all factors."""
        base_reasoning = self._generate_reasoning(signals, market_condition, risk_level, indicators)
        
        enhancements = []
        
        if ict_setup and ict_setup.has_setup:
            enhancements.append(f"ICT setup detected: {ict_setup.setup_type} (confidence: {ict_setup.confidence:.1f}%)")
        
        if entry_timing:
            if entry_timing.is_optimal:
                enhancements.append(f"Optimal entry timing (score: {entry_timing.score:.1f}%)")
            else:
                enhancements.append(f"Suboptimal timing (score: {entry_timing.score:.1f}%)")
        
        if 'trend_alignment' in indicators:
            alignment = indicators['trend_alignment'] * 100
            enhancements.append(f"Multi-timeframe alignment: {alignment:.1f}%")
        
        if enhancements:
            return base_reasoning + " " + ". ".join(enhancements) + "."
        
        return base_reasoning
    
    def _calculate_advanced_levels(self, current_price: float, action: TradeAction, analysis: TradingAnalysis):
        """Calculate advanced stop loss and take profit levels."""
        # Base levels
        base_stop = self._calculate_stop_loss(current_price, action)
        base_tp = self._calculate_take_profit(current_price, action)
        
        # Adjust based on ICT setup
        if analysis.ict_setup and analysis.ict_setup.invalidation_level:
            if action == TradeAction.BUY:
                base_stop = min(base_stop, analysis.ict_setup.invalidation_level)
            else:
                base_stop = max(base_stop, analysis.ict_setup.invalidation_level)
        
        # Adjust based on volatility
        volatility_multiplier = 1 + (analysis.market_condition.volatility / 1000)
        
        if action == TradeAction.BUY:
            adjusted_stop = current_price - abs(current_price - base_stop) * volatility_multiplier
            adjusted_tp = current_price + abs(base_tp - current_price) * volatility_multiplier
        else:
            adjusted_stop = current_price + abs(base_stop - current_price) * volatility_multiplier
            adjusted_tp = current_price - abs(current_price - base_tp) * volatility_multiplier
        
        return adjusted_stop, adjusted_tp


__all__ = ["OptimizedStrategy"]
