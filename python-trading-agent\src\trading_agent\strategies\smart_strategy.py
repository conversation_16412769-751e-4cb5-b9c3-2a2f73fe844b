"""
Smart Trading Strategy
=====================

Advanced trading strategy with multiple technical indicators and risk management.
"""

from datetime import datetime
from typing import List, Optional

from .base_strategy import BaseStrategy
from ..models.types import (
    MarketData,
    TradeOrder,
    TradingAnalysis,
    TradeAction,
    StrategyConfig
)
from ..utils.config import config
from ..utils.logger import get_logger, log_signal

logger = get_logger(__name__)


class SmartStrategy(BaseStrategy):
    """Smart trading strategy with advanced analysis."""
    
    def __init__(self, strategy_config: Optional[StrategyConfig] = None):
        if strategy_config is None:
            strategy_config = StrategyConfig(
                name="SmartStrategy",
                min_confidence=70.0,
                min_signal_strength=60.0,
                min_confirmations=2,
                max_divergences=3,
                required_trend_strength=60.0,
                avoid_high_volatility=True,
                require_volume_confirmation=True
            )
        
        super().__init__(strategy_config)
        logger.info(f"SmartStrategy initialized with config: {strategy_config.name}")
    
    async def analyze(self, market_data: List[MarketData]) -> TradingAnalysis:
        """Analyze market data using smart strategy."""
        if not market_data:
            raise ValueError("No market data provided")
        
        # Update history with latest data
        for data_point in market_data[-100:]:  # Use last 100 points
            self.update_history(data_point)
        
        if len(self.price_history) < 20:
            # Not enough data for analysis
            return self._create_hold_analysis("Insufficient data for analysis")
        
        # Calculate technical indicators
        indicators = self.calculate_technical_indicators()
        
        # Calculate signal strength
        signals = self.calculate_signal_strength(indicators)
        
        # Analyze market condition
        market_condition = self.analyze_market_condition(indicators)
        
        # Assess risk level
        risk_level = self.assess_risk_level(market_condition, signals)
        
        # Generate recommendation
        recommendation = self.generate_recommendation(signals, market_condition, risk_level)
        
        # Generate reasoning
        reasoning = self._generate_reasoning(signals, market_condition, risk_level, indicators)
        
        analysis = TradingAnalysis(
            signals=signals,
            market_condition=market_condition,
            risk_level=risk_level,
            recommendation=recommendation,
            reasoning=reasoning
        )
        
        # Log the analysis
        log_signal(
            strategy=self.config.name,
            signal=recommendation.value,
            confidence=signals.confidence,
            symbol=market_data[-1].symbol
        )
        
        self.analysis_history.append(analysis)
        
        return analysis
    
    async def decide_action(self, analysis: TradingAnalysis) -> Optional[TradeOrder]:
        """Make trading decision based on analysis."""
        # Check if we should trade
        if not self._should_trade(analysis):
            return None
        
        # Determine action
        if analysis.recommendation.value in ["strong_buy", "buy"]:
            action = TradeAction.BUY
        elif analysis.recommendation.value in ["strong_sell", "sell"]:
            action = TradeAction.SELL
        else:
            return None
        
        # Calculate position size
        amount = self._calculate_position_size(analysis)
        
        # Get current price (simplified - would need market data)
        current_price = self.price_history[-1] if self.price_history else 1.0
        
        # Create trade order
        trade_order = TradeOrder(
            action=action,
            symbol="EUR/USD",  # Default symbol - would be passed from analysis
            amount=amount,
            price=current_price,
            stop_loss=self._calculate_stop_loss(current_price, action),
            take_profit=self._calculate_take_profit(current_price, action)
        )
        
        logger.info(
            f"SmartStrategy decision: {action.value} {amount} @ {current_price:.4f} "
            f"(confidence: {analysis.signals.confidence:.1f}%)"
        )
        
        return trade_order
    
    def _should_trade(self, analysis: TradingAnalysis) -> bool:
        """Determine if we should execute a trade."""
        # Check minimum confidence
        if analysis.signals.confidence < self.config.min_confidence:
            logger.debug(f"Confidence too low: {analysis.signals.confidence} < {self.config.min_confidence}")
            return False
        
        # Check signal strength
        max_signal = max(analysis.signals.buy, analysis.signals.sell)
        if max_signal < self.config.min_signal_strength:
            logger.debug(f"Signal strength too low: {max_signal} < {self.config.min_signal_strength}")
            return False
        
        # Check risk level
        if analysis.risk_level.value in ["high", "very_high"]:
            logger.debug(f"Risk level too high: {analysis.risk_level.value}")
            return False
        
        # Check confirmations
        if analysis.signals.confirmations < self.config.min_confirmations:
            logger.debug(f"Not enough confirmations: {analysis.signals.confirmations} < {self.config.min_confirmations}")
            return False
        
        # Check trend strength if required
        if analysis.market_condition.trend_strength < self.config.required_trend_strength:
            logger.debug(f"Trend strength too low: {analysis.market_condition.trend_strength} < {self.config.required_trend_strength}")
            return False
        
        # Check volatility
        if self.config.avoid_high_volatility and analysis.market_condition.volatility > 80:
            logger.debug(f"Volatility too high: {analysis.market_condition.volatility}")
            return False
        
        # Check volume confirmation
        if self.config.require_volume_confirmation and analysis.market_condition.volume_profile == "low":
            logger.debug("Volume confirmation required but volume is low")
            return False
        
        # Check session preferences
        if analysis.market_condition.session not in self.config.preferred_sessions:
            logger.debug(f"Not in preferred session: {analysis.market_condition.session}")
            return False
        
        # Check dead hours
        if self.config.avoid_dead_hours and analysis.market_condition.session.value == "dead":
            logger.debug("Avoiding dead hours")
            return False
        
        # Check daily trade limits
        if self.daily_trades >= self.config.max_daily_trades:
            logger.debug(f"Daily trade limit reached: {self.daily_trades}")
            return False
        
        # Check consecutive losses
        if self.consecutive_losses >= self.config.max_consecutive_losses:
            logger.debug(f"Max consecutive losses reached: {self.consecutive_losses}")
            return False
        
        # Check minimum time between trades
        if self.last_trade_time:
            time_diff = (datetime.now() - self.last_trade_time).total_seconds() * 1000
            if time_diff < self.config.min_time_between_trades:
                logger.debug(f"Minimum time between trades not met: {time_diff}ms")
                return False
        
        return True
    
    def _calculate_position_size(self, analysis: TradingAnalysis) -> float:
        """Calculate position size based on analysis."""
        base_size = 10.0  # Base position size
        
        # Adjust for confidence
        confidence_multiplier = analysis.signals.confidence / 100
        
        # Adjust for signal quality
        quality_multipliers = {
            "A+": 1.5,
            "A": 1.3,
            "B": 1.1,
            "C": 1.0,
            "D": 0.8,
            "F": 0.5
        }
        quality_multiplier = quality_multipliers.get(analysis.signals.quality.value, 1.0)
        
        # Adjust for risk level
        risk_multipliers = {
            "very_low": 1.2,
            "low": 1.0,
            "medium": 0.8,
            "high": 0.5,
            "very_high": 0.3
        }
        risk_multiplier = risk_multipliers.get(analysis.risk_level.value, 1.0)
        
        # Adjust for trend strength
        trend_multiplier = analysis.market_condition.trend_strength / 100
        
        # Calculate final size
        final_size = base_size * confidence_multiplier * quality_multiplier * risk_multiplier * trend_multiplier
        
        # Ensure minimum size
        return max(final_size, 1.0)
    
    def _calculate_stop_loss(self, current_price: float, action: TradeAction) -> float:
        """Calculate stop loss price."""
        stop_loss_percentage = config.stop_loss_percentage
        
        if action == TradeAction.BUY:
            return current_price * (1 - stop_loss_percentage)
        else:
            return current_price * (1 + stop_loss_percentage)
    
    def _calculate_take_profit(self, current_price: float, action: TradeAction) -> float:
        """Calculate take profit price."""
        take_profit_percentage = config.take_profit_percentage
        
        if action == TradeAction.BUY:
            return current_price * (1 + take_profit_percentage)
        else:
            return current_price * (1 - take_profit_percentage)
    
    def _generate_reasoning(
        self,
        signals,
        market_condition,
        risk_level,
        indicators
    ) -> str:
        """Generate human-readable reasoning for the decision."""
        reasoning_parts = []
        
        # Signal analysis
        if signals.buy > signals.sell:
            reasoning_parts.append(f"Buy signals dominate ({signals.buy:.1f}% vs {signals.sell:.1f}%)")
        elif signals.sell > signals.buy:
            reasoning_parts.append(f"Sell signals dominate ({signals.sell:.1f}% vs {signals.buy:.1f}%)")
        else:
            reasoning_parts.append("Signals are balanced")
        
        reasoning_parts.append(f"Confidence: {signals.confidence:.1f}%")
        reasoning_parts.append(f"Signal quality: {signals.quality.value}")
        
        # Market condition
        reasoning_parts.append(f"Market trend: {market_condition.trend} (strength: {market_condition.trend_strength:.1f}%)")
        reasoning_parts.append(f"Volatility: {market_condition.volatility:.1f}%")
        reasoning_parts.append(f"Volume: {market_condition.volume_profile}")
        reasoning_parts.append(f"Session: {market_condition.session.value}")
        
        # Risk assessment
        reasoning_parts.append(f"Risk level: {risk_level.value}")
        
        # Technical indicators
        if 'rsi' in indicators:
            rsi = indicators['rsi']
            if rsi < 30:
                reasoning_parts.append(f"RSI oversold ({rsi:.1f})")
            elif rsi > 70:
                reasoning_parts.append(f"RSI overbought ({rsi:.1f})")
            else:
                reasoning_parts.append(f"RSI neutral ({rsi:.1f})")
        
        if 'macd' in indicators and 'macd_signal' in indicators:
            if indicators['macd'] > indicators['macd_signal']:
                reasoning_parts.append("MACD bullish crossover")
            else:
                reasoning_parts.append("MACD bearish crossover")
        
        return ". ".join(reasoning_parts) + "."
    
    def _create_hold_analysis(self, reason: str) -> TradingAnalysis:
        """Create a HOLD analysis with given reason."""
        from ..models.types import (
            SignalStrength, SignalQuality, MarketCondition, 
            RiskLevel, Recommendation, MarketSession
        )
        
        return TradingAnalysis(
            signals=SignalStrength(
                buy=0, sell=0, confidence=0, quality=SignalQuality.F, 
                confirmations=0, divergences=0
            ),
            market_condition=MarketCondition(
                trend="sideways", trend_strength=0, volatility=0,
                volume_profile="low", session=MarketSession.DEAD
            ),
            risk_level=RiskLevel.HIGH,
            recommendation=Recommendation.HOLD,
            reasoning=reason
        )


__all__ = ["SmartStrategy"]
