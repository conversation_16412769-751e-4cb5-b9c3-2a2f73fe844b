"""
Configuration Management
========================

Modern configuration management using Pydantic Settings with environment variable support.
"""

import os
from pathlib import Path
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class TradingConfig(BaseSettings):
    """Main trading configuration with validation."""
    
    # Trading Parameters
    initial_balance: float = Field(default=10000.0, gt=0, description="Initial trading balance")
    max_daily_trades: int = Field(default=10, ge=1, le=100, description="Maximum trades per day")
    min_time_between_trades: int = Field(default=300000, ge=60000, description="Minimum time between trades (ms)")
    max_consecutive_losses: int = Field(default=3, ge=1, le=10, description="Maximum consecutive losses before pause")
    
    # Risk Management
    max_risk_per_trade: float = Field(default=0.02, gt=0, le=0.1, description="Maximum risk per trade (2%)")
    stop_loss_percentage: float = Field(default=0.015, gt=0, le=0.05, description="Stop loss percentage")
    take_profit_percentage: float = Field(default=0.03, gt=0, le=0.1, description="Take profit percentage")
    
    # API Configuration
    api_url: str = Field(default="https://api.exchangerate.host/timeseries", description="Primary API URL")
    api_symbol: str = Field(default="EUR", description="Base trading symbol")
    access_key: str = Field(default="", description="API access key")
    
    # Alpha Vantage API
    alpha_vantage_api_key: Optional[str] = Field(default=None, description="Alpha Vantage API key")
    forex_api_key: Optional[str] = Field(default=None, description="Forex API key")
    trading_api_base: str = Field(default="https://www.alphavantage.co/query", description="Trading API base URL")
    
    # MCP Server Configuration
    mcp_server_port: int = Field(default=3000, ge=1000, le=65535, description="MCP server port")
    mcp_server_host: str = Field(default="localhost", description="MCP server host")
    
    # Trading Pairs
    default_currency_pair: str = Field(default="EUR/USD", description="Default currency pair")
    default_timeframe: str = Field(default="5min", description="Default timeframe")
    supported_pairs: List[str] = Field(
        default=["EUR/USD", "GBP/USD", "USD/JPY", "AUD/USD", "USD/CAD", "USD/CHF"],
        description="Supported currency pairs"
    )
    
    # Strategy Configuration
    min_confidence: float = Field(default=70.0, ge=50.0, le=100.0, description="Minimum confidence for trades")
    min_signal_strength: float = Field(default=60.0, ge=50.0, le=100.0, description="Minimum signal strength")
    
    # Evolution/Optimization
    evolution_population_size: int = Field(default=20, ge=5, le=100, description="Evolution population size")
    evolution_generations: int = Field(default=10, ge=5, le=50, description="Evolution generations")
    evolution_mutation_rate: float = Field(default=0.1, gt=0, lt=1, description="Evolution mutation rate")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: str = Field(default="trading_agent.log", description="Log file path")
    log_max_size: str = Field(default="10 MB", description="Maximum log file size")
    log_retention: str = Field(default="7 days", description="Log retention period")
    
    # Database
    database_url: str = Field(default="sqlite:///trading_agent.db", description="Database URL")
    redis_url: Optional[str] = Field(default=None, description="Redis URL for caching")
    
    # Development
    debug: bool = Field(default=False, description="Debug mode")
    testing: bool = Field(default=False, description="Testing mode")
    
    @validator('supported_pairs')
    def validate_currency_pairs(cls, v):
        """Validate currency pair format."""
        for pair in v:
            if '/' not in pair or len(pair.split('/')) != 2:
                raise ValueError(f"Invalid currency pair format: {pair}")
        return v
    
    @validator('default_currency_pair')
    def validate_default_pair(cls, v, values):
        """Ensure default pair is in supported pairs."""
        if 'supported_pairs' in values and v not in values['supported_pairs']:
            raise ValueError(f"Default pair {v} not in supported pairs")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_prefix = "TRADING_"


class Config:
    """Configuration manager singleton."""
    
    _instance: Optional[TradingConfig] = None
    _config_path: Optional[Path] = None
    
    @classmethod
    def get_instance(cls, config_path: Optional[Path] = None) -> TradingConfig:
        """Get configuration instance."""
        if cls._instance is None or config_path != cls._config_path:
            cls._config_path = config_path
            
            # Set environment file path if provided
            if config_path and config_path.exists():
                os.environ.setdefault("TRADING_ENV_FILE", str(config_path))
            
            cls._instance = TradingConfig()
        
        return cls._instance
    
    @classmethod
    def reload(cls) -> TradingConfig:
        """Reload configuration."""
        cls._instance = None
        return cls.get_instance(cls._config_path)


# Global configuration instance
config = Config.get_instance()

__all__ = ["TradingConfig", "Config", "config"]
