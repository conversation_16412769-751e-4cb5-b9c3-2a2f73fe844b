"""
Modern Logging System
====================

Advanced logging system using Loguru with structured logging support.
"""

import sys
from pathlib import Path
from typing import Optional

from loguru import logger
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from .config import config


class TradingLogger:
    """Enhanced logger for trading operations."""
    
    def __init__(self):
        self.console = Console()
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with file and console handlers."""
        # Remove default handler
        logger.remove()
        
        # Console handler with Rich formatting
        logger.add(
            RichHandler(console=self.console, rich_tracebacks=True),
            level=config.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            colorize=True
        )
        
        # File handler with rotation
        log_path = Path(config.log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_path,
            level=config.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=config.log_max_size,
            retention=config.log_retention,
            compression="zip",
            serialize=False
        )
        
        # Error file handler
        error_log_path = log_path.parent / f"{log_path.stem}_errors{log_path.suffix}"
        logger.add(
            error_log_path,
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}\n{exception}",
            rotation=config.log_max_size,
            retention=config.log_retention,
            compression="zip"
        )
    
    def trade_executed(self, action: str, symbol: str, amount: float, price: float, balance: float):
        """Log trade execution."""
        logger.info(
            "Trade executed",
            extra={
                "action": action,
                "symbol": symbol,
                "amount": amount,
                "price": price,
                "balance": balance,
                "event_type": "trade_executed"
            }
        )
    
    def strategy_signal(self, strategy: str, signal: str, confidence: float, symbol: str):
        """Log strategy signal."""
        logger.info(
            f"Strategy signal: {strategy} -> {signal}",
            extra={
                "strategy": strategy,
                "signal": signal,
                "confidence": confidence,
                "symbol": symbol,
                "event_type": "strategy_signal"
            }
        )
    
    def market_data_received(self, symbol: str, price: float, volume: Optional[float] = None):
        """Log market data reception."""
        logger.debug(
            f"Market data: {symbol} @ {price}",
            extra={
                "symbol": symbol,
                "price": price,
                "volume": volume,
                "event_type": "market_data"
            }
        )
    
    def risk_alert(self, message: str, risk_level: str, details: dict):
        """Log risk management alerts."""
        logger.warning(
            f"Risk Alert [{risk_level}]: {message}",
            extra={
                "risk_level": risk_level,
                "details": details,
                "event_type": "risk_alert"
            }
        )
    
    def performance_metric(self, metric_name: str, value: float, symbol: Optional[str] = None):
        """Log performance metrics."""
        logger.info(
            f"Performance: {metric_name} = {value}",
            extra={
                "metric_name": metric_name,
                "value": value,
                "symbol": symbol,
                "event_type": "performance_metric"
            }
        )
    
    def api_call(self, endpoint: str, status_code: int, response_time: float):
        """Log API calls."""
        level = "INFO" if status_code < 400 else "ERROR"
        logger.log(
            level,
            f"API call: {endpoint} [{status_code}] in {response_time:.2f}ms",
            extra={
                "endpoint": endpoint,
                "status_code": status_code,
                "response_time": response_time,
                "event_type": "api_call"
            }
        )
    
    def mcp_event(self, event_type: str, tool_name: str, success: bool, details: dict):
        """Log MCP server events."""
        level = "INFO" if success else "ERROR"
        logger.log(
            level,
            f"MCP {event_type}: {tool_name}",
            extra={
                "mcp_event_type": event_type,
                "tool_name": tool_name,
                "success": success,
                "details": details,
                "event_type": "mcp_event"
            }
        )


# Global logger instance
trading_logger = TradingLogger()

def get_logger(name: Optional[str] = None):
    """Get logger instance with optional name binding."""
    if name:
        return logger.bind(name=name)
    return logger

# Convenience functions
def log_trade(action: str, symbol: str, amount: float, price: float, balance: float):
    """Log trade execution."""
    trading_logger.trade_executed(action, symbol, amount, price, balance)

def log_signal(strategy: str, signal: str, confidence: float, symbol: str):
    """Log strategy signal."""
    trading_logger.strategy_signal(strategy, signal, confidence, symbol)

def log_market_data(symbol: str, price: float, volume: Optional[float] = None):
    """Log market data."""
    trading_logger.market_data_received(symbol, price, volume)

def log_risk_alert(message: str, risk_level: str, details: dict):
    """Log risk alert."""
    trading_logger.risk_alert(message, risk_level, details)

def log_performance(metric_name: str, value: float, symbol: Optional[str] = None):
    """Log performance metric."""
    trading_logger.performance_metric(metric_name, value, symbol)

def log_api_call(endpoint: str, status_code: int, response_time: float):
    """Log API call."""
    trading_logger.api_call(endpoint, status_code, response_time)

def log_mcp_event(event_type: str, tool_name: str, success: bool, details: dict):
    """Log MCP event."""
    trading_logger.mcp_event(event_type, tool_name, success, details)

__all__ = [
    "TradingLogger",
    "trading_logger", 
    "get_logger",
    "log_trade",
    "log_signal", 
    "log_market_data",
    "log_risk_alert",
    "log_performance",
    "log_api_call",
    "log_mcp_event"
]
