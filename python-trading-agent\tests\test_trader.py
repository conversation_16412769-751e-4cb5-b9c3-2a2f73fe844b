"""
Tests for Trading Engine
=======================

Unit tests for the core trading functionality.
"""

import pytest
import asyncio
from datetime import datetime

from src.trading_agent.core.trader import Trader, InsufficientBalanceError, TradeExecutionError
from src.trading_agent.models.types import TradeOrder, TradeAction


class TestTrader:
    """Test cases for the Trader class."""
    
    @pytest.fixture
    def trader(self):
        """Create a trader instance for testing."""
        return Trader(initial_balance=10000.0)
    
    @pytest.fixture
    def buy_order(self):
        """Create a sample buy order."""
        return TradeOrder(
            action=TradeAction.BUY,
            symbol="EUR/USD",
            amount=100.0,
            price=1.1000
        )
    
    @pytest.fixture
    def sell_order(self):
        """Create a sample sell order."""
        return TradeOrder(
            action=TradeAction.SELL,
            symbol="EUR/USD",
            amount=50.0,
            price=1.1050
        )
    
    def test_trader_initialization(self, trader):
        """Test trader initialization."""
        assert trader.initial_balance == 10000.0
        assert trader.portfolio.balance == 10000.0
        assert trader.portfolio.total_trades == 0
        assert trader.daily_trades == 0
        assert trader.consecutive_losses == 0
        assert len(trader.trade_history) == 0
    
    @pytest.mark.asyncio
    async def test_successful_buy_trade(self, trader, buy_order):
        """Test successful buy trade execution."""
        initial_balance = trader.get_balance()
        
        success = await trader.execute_trade(buy_order)
        
        assert success is True
        assert trader.get_balance() < initial_balance
        assert trader.get_position("EUR/USD") == 100.0
        assert trader.portfolio.total_trades == 1
        assert trader.daily_trades == 1
        assert len(trader.trade_history) == 1
    
    @pytest.mark.asyncio
    async def test_successful_sell_trade(self, trader, buy_order, sell_order):
        """Test successful sell trade execution."""
        # First buy some position
        await trader.execute_trade(buy_order)
        initial_balance = trader.get_balance()
        
        # Then sell part of it
        success = await trader.execute_trade(sell_order)
        
        assert success is True
        assert trader.get_balance() > initial_balance
        assert trader.get_position("EUR/USD") == 50.0  # 100 - 50
        assert trader.portfolio.total_trades == 2
        assert trader.daily_trades == 2
    
    @pytest.mark.asyncio
    async def test_insufficient_balance_buy(self, trader):
        """Test buy trade with insufficient balance."""
        large_order = TradeOrder(
            action=TradeAction.BUY,
            symbol="EUR/USD",
            amount=100000.0,  # Very large amount
            price=1.1000
        )
        
        success = await trader.execute_trade(large_order)
        
        assert success is False
        assert trader.get_balance() == 10000.0  # Balance unchanged
        assert trader.get_position("EUR/USD") == 0.0
        assert trader.portfolio.total_trades == 0
    
    @pytest.mark.asyncio
    async def test_insufficient_position_sell(self, trader, sell_order):
        """Test sell trade with insufficient position."""
        # Try to sell without having any position
        success = await trader.execute_trade(sell_order)
        
        assert success is False
        assert trader.get_balance() == 10000.0  # Balance unchanged
        assert trader.get_position("EUR/USD") == 0.0
        assert trader.portfolio.total_trades == 0
    
    def test_get_portfolio(self, trader):
        """Test portfolio retrieval."""
        portfolio = trader.get_portfolio()
        
        assert portfolio.balance == 10000.0
        assert portfolio.total_trades == 0
        assert portfolio.winning_trades == 0
        assert portfolio.losing_trades == 0
        assert portfolio.win_rate == 0.0
        assert len(portfolio.positions) == 0
    
    def test_get_performance_stats(self, trader):
        """Test performance statistics."""
        stats = trader.get_performance_stats()
        
        assert stats["balance"] == 10000.0
        assert stats["total_return"] == 0.0
        assert stats["total_trades"] == 0
        assert stats["win_rate"] == 0.0
        assert stats["daily_trades"] == 0
        assert stats["consecutive_losses"] == 0
    
    @pytest.mark.asyncio
    async def test_daily_trade_limit(self, trader, buy_order):
        """Test daily trade limit enforcement."""
        # Set a low daily limit for testing
        trader.config = type('Config', (), {'max_daily_trades': 2})()
        
        # Execute trades up to limit
        await trader.execute_trade(buy_order)
        await trader.execute_trade(buy_order)
        
        # This should fail due to daily limit
        success = await trader.execute_trade(buy_order)
        assert success is False
        assert trader.daily_trades == 2
    
    def test_reset_daily_counters(self, trader):
        """Test daily counter reset."""
        trader.daily_trades = 5
        trader.reset_daily_counters()
        
        assert trader.daily_trades == 0
    
    @pytest.mark.asyncio
    async def test_trade_history(self, trader, buy_order, sell_order):
        """Test trade history tracking."""
        await trader.execute_trade(buy_order)
        await trader.execute_trade(sell_order)
        
        history = trader.get_trade_history()
        assert len(history) == 2
        assert history[0].action == TradeAction.BUY
        assert history[1].action == TradeAction.SELL
        
        # Test limited history
        limited_history = trader.get_trade_history(limit=1)
        assert len(limited_history) == 1
        assert limited_history[0].action == TradeAction.SELL  # Most recent
    
    @pytest.mark.asyncio
    async def test_concurrent_trades(self, trader):
        """Test concurrent trade execution."""
        orders = [
            TradeOrder(action=TradeAction.BUY, symbol="EUR/USD", amount=10.0, price=1.1000),
            TradeOrder(action=TradeAction.BUY, symbol="GBP/USD", amount=10.0, price=1.3000),
            TradeOrder(action=TradeAction.BUY, symbol="USD/JPY", amount=10.0, price=110.00),
        ]
        
        # Execute trades concurrently
        tasks = [trader.execute_trade(order) for order in orders]
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        assert all(results)
        assert trader.portfolio.total_trades == 3
        assert len(trader.portfolio.positions) == 3
    
    @pytest.mark.asyncio
    async def test_stop_loss_take_profit(self, trader):
        """Test stop loss and take profit calculation."""
        order = TradeOrder(
            action=TradeAction.BUY,
            symbol="EUR/USD",
            amount=100.0,
            price=1.1000,
            stop_loss=1.0950,
            take_profit=1.1100
        )
        
        success = await trader.execute_trade(order)
        
        assert success is True
        trade = trader.trade_history[-1]
        assert trade.stop_loss == 1.0950
        assert trade.take_profit == 1.1100


@pytest.mark.asyncio
async def test_trader_error_handling():
    """Test trader error handling."""
    trader = Trader()
    
    # Test invalid trade action
    invalid_order = TradeOrder(
        action="invalid_action",  # This should cause validation error
        symbol="EUR/USD",
        amount=100.0,
        price=1.1000
    )
    
    with pytest.raises(ValueError):
        # This should raise a validation error from Pydantic
        pass


if __name__ == "__main__":
    pytest.main([__file__])
