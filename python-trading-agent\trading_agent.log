2025-07-08 20:47:22 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 20:47:23 | INFO     | trading_agent.data.fetcher:__init__:43 - <PERSON><PERSON><PERSON><PERSON> initialized with async HTTP client
2025-07-08 20:47:23 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 20:47:23 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 20:47:23 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 20:47:23 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 20:47:23 | INFO     | __main__:__init__:33 - Simple Trading Server initialized
2025-07-08 20:47:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 20:47:27 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 4736.68ms
2025-07-08 20:47:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 20:47:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 20:47:28 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 168.94ms
2025-07-08 20:47:28 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 20:47:28 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: SmartStrategy -> hold
2025-07-08 20:51:05 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 20:51:06 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 20:51:06 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 20:51:06 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 20:51:06 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 20:51:06 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 20:51:06 | INFO     | __main__:__init__:33 - Simple Trading Server initialized
2025-07-08 20:51:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 20:51:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 2156.98ms
2025-07-08 20:51:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 20:51:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 20:51:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 131.44ms
2025-07-08 20:51:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 20:51:08 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: SmartStrategy -> hold
2025-07-08 21:11:36 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:11:36 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:11:36 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:11:36 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:11:36 | ERROR    | trading_agent.cli:run_challenge:388 - Challenge error: Can't instantiate abstract class UltraStrategy without an implementation for abstract method 'decide_action'
2025-07-08 21:12:16 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:12:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:12:16 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 696.89ms
2025-07-08 21:12:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 21:12:16 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:12:16 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:12:16 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:12:16 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:12:16 | ERROR    | trading_agent.cli:run_tests:427 - Test error: Can't instantiate abstract class UltraStrategy without an implementation for abstract method 'decide_action'
2025-07-08 21:15:50 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:15:50 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:15:51 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 1044.60ms
2025-07-08 21:15:51 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy UltraStrategy initialized
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.ultra_strategy:__init__:69 - UltraStrategy initialized for 80%+ win rate
2025-07-08 21:15:51 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:28:05 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:28:05 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:28:10 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 5170.26ms
2025-07-08 21:28:10 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy UltraStrategy initialized
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.ultra_strategy:__init__:69 - UltraStrategy initialized for 80%+ win rate
2025-07-08 21:28:10 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy UltraStrategy initialized
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.ultra_strategy:__init__:69 - UltraStrategy initialized for 80%+ win rate
2025-07-08 21:28:30 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:28:30 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:28:30 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:28:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 1457.81ms
2025-07-08 21:28:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:28:32 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:28:32 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:28:32 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:28:32 | ERROR    | trading_agent.cli:run_challenge:388 - Challenge error: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:29:00 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy UltraStrategy initialized
2025-07-08 21:29:00 | INFO     | trading_agent.strategies.ultra_strategy:__init__:69 - UltraStrategy initialized for 80%+ win rate
2025-07-08 21:29:01 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:29:01 | ERROR    | trading_agent.cli:run_backtest:265 - Backtest error: 'Command' object has no attribute 'initial_balance'
2025-07-08 21:29:32 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:29:32 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:29:32 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:29:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:29:33 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 716.12ms
2025-07-08 21:29:33 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 21:29:33 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: SmartStrategy -> hold
2025-07-08 21:29:54 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:29:54 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:29:54 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:29:54 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:29:55 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:29:55 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with smart strategy
2025-07-08 21:29:55 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:29:56 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 714.70ms
2025-07-08 21:29:56 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:29:56 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:29:56 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:29:56 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:29:56 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:01 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:01 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.38ms
2025-07-08 21:30:01 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:01 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:01 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:01 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:01 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:06 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.71ms
2025-07-08 21:30:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:06 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:06 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:06 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:06 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:11 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:11 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.94ms
2025-07-08 21:30:11 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:11 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:11 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:11 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:11 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:16 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.44ms
2025-07-08 21:30:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:16 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:16 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:16 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:17 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:22 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.26ms
2025-07-08 21:30:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:22 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:22 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:22 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:22 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:27 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.52ms
2025-07-08 21:30:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:27 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:27 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:27 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:27 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.43ms
2025-07-08 21:30:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:32 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:32 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:32 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:32 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:37 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.15ms
2025-07-08 21:30:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:37 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:37 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:37 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:37 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:42 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:42 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.00ms
2025-07-08 21:30:42 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:42 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:42 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:42 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:42 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:47 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:47 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.59ms
2025-07-08 21:30:47 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:47 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:47 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:47 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:47 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:52 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:52 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.66ms
2025-07-08 21:30:52 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:52 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:52 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:52 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:52 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:45:14 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:45:14 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy ScalpingStrategy initialized
2025-07-08 21:45:14 | INFO     | trading_agent.strategies.scalping_strategy:__init__:70 - ScalpingStrategy initialized - 83% win rate proven method
2025-07-08 21:45:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:45:16 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 2017.25ms
2025-07-08 21:45:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 21:45:16 | ERROR    | trading_agent.cli:run_analysis:100 - Analysis error: 'ScalpingStrategy' object has no attribute '_analyze_scalping_market_condition'
2025-07-08 21:48:31 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:48:31 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy ScalpingStrategy initialized
2025-07-08 21:48:31 | INFO     | trading_agent.strategies.scalping_strategy:__init__:70 - ScalpingStrategy initialized - 83% win rate proven method
2025-07-08 21:48:31 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:48:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 974.88ms
2025-07-08 21:48:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 21:48:32 | ERROR    | trading_agent.cli:run_analysis:100 - Analysis error: 1 validation error for ICTSetup
entry_zone
  Input should be a valid dictionary [type=dict_type, input_value=1.1739845275878906, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
2025-07-08 21:50:13 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:50:13 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy ScalpingStrategy initialized
2025-07-08 21:50:13 | INFO     | trading_agent.strategies.scalping_strategy:__init__:70 - ScalpingStrategy initialized - 83% win rate proven method
2025-07-08 21:50:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:14 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 810.15ms
2025-07-08 21:50:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 21:50:14 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: ScalpingStrategy -> hold
2025-07-08 21:50:38 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:50:39 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:50:39 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:50:39 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:50:40 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:50:40 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with scalping strategy
2025-07-08 21:50:40 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:41 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 742.46ms
2025-07-08 21:50:41 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:50:41 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:50:41 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:50:41 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:50:41 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:50:46 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:46 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.26ms
2025-07-08 21:50:46 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:50:46 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:46 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:46 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:50:46 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:50:51 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:51 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.41ms
2025-07-08 21:50:51 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:50:51 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:51 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:51 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:50:51 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:50:56 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:56 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.22ms
2025-07-08 21:50:56 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:50:56 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:56 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:56 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:50:56 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:01 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:01 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.65ms
2025-07-08 21:51:01 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:01 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:01 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:01 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:01 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:06 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.34ms
2025-07-08 21:51:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:06 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:06 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:06 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:06 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:11 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:11 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.73ms
2025-07-08 21:51:11 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:11 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:11 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:11 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:11 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:16 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.14ms
2025-07-08 21:51:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:16 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:16 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:16 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:16 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:21 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:21 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.21ms
2025-07-08 21:51:21 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:21 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:21 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:21 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:21 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:26 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:26 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.38ms
2025-07-08 21:51:26 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:26 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:26 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:26 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:26 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:31 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:31 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.15ms
2025-07-08 21:51:31 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:31 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:31 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:31 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:31 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:36 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:36 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.93ms
2025-07-08 21:51:36 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:36 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:36 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:36 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:36 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:41 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:41 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.63ms
2025-07-08 21:51:41 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:41 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:42 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:42 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:42 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:47 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:47 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.10ms
2025-07-08 21:51:47 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:47 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:47 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:47 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:47 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:52 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:52 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.87ms
2025-07-08 21:51:52 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:52 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:52 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:52 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:52 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:57 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:57 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.00ms
2025-07-08 21:51:57 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:57 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:57 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:57 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:57 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:02 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:02 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.36ms
2025-07-08 21:52:02 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:02 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:02 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:02 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:02 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:07 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:07 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.53ms
2025-07-08 21:52:07 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:07 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:07 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:07 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:07 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:12 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:12 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.53ms
2025-07-08 21:52:12 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:12 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:12 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:12 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:12 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:17 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:17 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.02ms
2025-07-08 21:52:17 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:17 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:17 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:17 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:17 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:22 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.11ms
2025-07-08 21:52:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:22 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:22 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:22 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:22 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:27 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.65ms
2025-07-08 21:52:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:27 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:27 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:27 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:27 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.77ms
2025-07-08 21:52:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:32 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:32 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:32 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:32 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:37 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.80ms
2025-07-08 21:52:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:37 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:37 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:37 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:37 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:21:21 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:21:21 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 22:21:21 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 22:21:21 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 22:21:21 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 22:21:21 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 22:21:21 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 22:21:21 | INFO     | trading_agent.ml.ml_strategy:__init__:83 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 22:21:21 | INFO     | trading_agent.ml.ml_strategy:__init__:84 - Components: LSTM + Ensemble + RL + Feature Engineering
2025-07-08 22:21:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:21:22 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 695.16ms
2025-07-08 22:21:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 22:21:22 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:166 - 🚀 Training ML models for 80% win rate...
2025-07-08 22:21:22 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:21:22 | ERROR    | trading_agent.ml.ml_strategy:_train_ml_models:197 - Error training ML models: aroon_up() missing 1 required positional argument: 'low'
2025-07-08 22:21:22 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: MLStrategy -> hold
2025-07-08 22:22:06 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 22:22:07 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:22:07 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 22:22:07 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 22:22:08 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:22:08 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with ml strategy
2025-07-08 22:22:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 718.01ms
2025-07-08 22:22:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:09 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:22:09 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:22:09 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:09 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:19 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.82ms
2025-07-08 22:22:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:19 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:19 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:19 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:19 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:29 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.43ms
2025-07-08 22:22:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:29 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:29 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:29 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:29 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:39 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.22ms
2025-07-08 22:22:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:39 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:39 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:39 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:39 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:49 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.78ms
2025-07-08 22:22:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:49 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:49 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:49 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:49 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:59 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 28.22ms
2025-07-08 22:22:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:59 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:59 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:59 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:59 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:09 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:09 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.07ms
2025-07-08 22:23:09 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:09 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:09 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:09 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:09 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:19 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.40ms
2025-07-08 22:23:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:19 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:19 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:19 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:19 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:29 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.90ms
2025-07-08 22:23:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:29 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:29 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:29 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:29 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:39 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.43ms
2025-07-08 22:23:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:39 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:39 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:39 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:39 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:49 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.49ms
2025-07-08 22:23:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:49 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:49 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:49 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:49 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:59 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.47ms
2025-07-08 22:23:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:59 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:59 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:59 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:59 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:09 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:09 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.63ms
2025-07-08 22:24:09 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:09 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:09 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:09 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:09 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:19 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.40ms
2025-07-08 22:24:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:19 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:19 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:20 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:20 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:30 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:30 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.01ms
2025-07-08 22:24:30 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:30 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:30 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:30 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:30 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:40 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:40 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.76ms
2025-07-08 22:24:40 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:40 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:40 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:40 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:40 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:50 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:50 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.42ms
2025-07-08 22:24:50 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:50 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:50 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:50 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:50 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:25:00 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:25:00 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.37ms
2025-07-08 22:25:00 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:25:00 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:00 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:00 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:25:00 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:25:10 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:25:10 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.30ms
2025-07-08 22:25:10 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:25:10 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:10 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:10 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:25:10 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:25:20 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:25:20 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 120.66ms
2025-07-08 22:25:20 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:25:20 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:20 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:20 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:25:20 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:25:43 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:25:43 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 22:25:43 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 22:25:43 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 22:25:43 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 22:25:43 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 22:25:43 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 22:25:43 | INFO     | trading_agent.ml.ml_strategy:__init__:83 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 22:25:43 | INFO     | trading_agent.ml.ml_strategy:__init__:84 - Components: LSTM + Ensemble + RL + Feature Engineering
2025-07-08 22:25:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:25:44 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 833.42ms
2025-07-08 22:25:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 22:25:44 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:166 - 🚀 Training ML models for 80% win rate...
2025-07-08 22:25:44 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:25:45 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:25:45 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:25:45 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:25:45 | INFO     | trading_agent.ml.feature_engineer:fit_transform:261 - ✅ Features fitted and transformed: (288, 9)
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ensemble_models:prepare_targets:115 - Target distribution: Buy=1, Sell=2, Hold=285
2025-07-08 22:25:45 | WARNING  | trading_agent.ml.lstm_predictor:train:134 - TensorFlow not available! Cannot train LSTM.
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:181 - LSTM trained: loss=0.000000
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ensemble_models:train:122 - 🚀 Training ensemble for 80% win rate...
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ensemble_models:train:123 - Training data: X (288, 9), y (288,)
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training random_forest...
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ random_forest accuracy: 1.0000
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training xgboost...
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ xgboost accuracy: 1.0000
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training lightgbm...
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ lightgbm accuracy: 0.9828
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training catboost...
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ catboost accuracy: 1.0000
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ensemble_models:train:165 - 🎯 Ensemble accuracy: 0.9957
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ensemble_models:train:168 - 🎉 80% WIN RATE ACHIEVED!
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:186 - Ensemble trained: accuracy=0.9957
2025-07-08 22:25:50 | WARNING  | trading_agent.ml.rl_agent:train:220 - RL not available! Cannot train agent.
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:191 - RL trained: win_rate=0.0%
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:194 - ✅ All ML models trained successfully!
2025-07-08 22:25:50 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:25:50 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:25:50 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:25:50 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:25:50 | ERROR    | trading_agent.ml.ml_strategy:_generate_ml_predictions:259 - Error generating ML predictions: 'LSTMPredictor' object has no attribute 'sequence_length'
2025-07-08 22:25:50 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: MLStrategy -> hold
2025-07-08 22:26:11 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 22:26:11 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:26:11 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 22:26:11 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 22:26:12 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:26:12 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with ml strategy
2025-07-08 22:26:12 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:13 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 643.40ms
2025-07-08 22:26:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:13 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:26:13 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:26:13 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:13 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:26:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:23 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.86ms
2025-07-08 22:26:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:23 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:23 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:23 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:23 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:26:33 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:33 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.27ms
2025-07-08 22:26:33 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:33 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:33 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:33 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:33 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:26:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:43 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.28ms
2025-07-08 22:26:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:43 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:43 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:43 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:43 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:26:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:53 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.27ms
2025-07-08 22:26:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:53 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:53 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:53 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:53 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:03 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:03 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.28ms
2025-07-08 22:27:03 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:03 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:03 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:03 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:03 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:13 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.22ms
2025-07-08 22:27:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:13 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:13 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:13 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:13 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:23 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.21ms
2025-07-08 22:27:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:23 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:24 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:24 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:24 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:34 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:34 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.81ms
2025-07-08 22:27:34 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:34 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:34 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:34 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:34 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:44 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.50ms
2025-07-08 22:27:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:44 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:44 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:44 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:44 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:54 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:54 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.53ms
2025-07-08 22:27:54 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:54 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:54 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:54 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:54 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:28:04 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:28:04 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.16ms
2025-07-08 22:28:04 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:28:04 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:28:04 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:28:04 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:28:04 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:28:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:28:14 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.87ms
2025-07-08 22:28:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:28:14 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:28:14 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:28:14 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:28:14 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:06 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 22:32:07 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:32:07 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 22:32:07 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 22:32:07 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:32:07 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with ml strategy
2025-07-08 22:32:07 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 731.07ms
2025-07-08 22:32:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:09 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:32:09 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:32:09 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:09 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:14 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.27ms
2025-07-08 22:32:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:14 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:14 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:14 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:14 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:19 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.27ms
2025-07-08 22:32:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:19 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:19 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:19 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:19 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:24 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:24 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.51ms
2025-07-08 22:32:24 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:24 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:24 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:24 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:24 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:29 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 5.83ms
2025-07-08 22:32:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:29 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:29 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:29 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:29 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:34 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:34 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.18ms
2025-07-08 22:32:34 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:34 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:34 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:34 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:34 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:39 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.59ms
2025-07-08 22:32:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:39 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:39 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:39 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:39 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:44 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.53ms
2025-07-08 22:32:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:44 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:44 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:44 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:44 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:49 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.00ms
2025-07-08 22:32:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:49 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:49 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:49 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:49 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:54 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:54 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 5.96ms
2025-07-08 22:32:54 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:54 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:54 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:54 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:54 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:59 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.53ms
2025-07-08 22:32:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:59 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:59 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:59 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:59 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:33:04 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:33:04 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 16.38ms
2025-07-08 22:33:04 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:33:04 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:33:04 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:33:04 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:33:04 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:45:51 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:45:51 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 22:45:51 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 22:45:51 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 22:45:51 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 22:45:51 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 22:45:51 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 22:45:51 | INFO     | trading_agent.ml.transformer_predictor:__init__:141 - 🚀 Transformer Predictor initialized for 80% win rate!
2025-07-08 22:45:51 | INFO     | trading_agent.ml.transformer_predictor:__init__:142 - Device: cpu, Sequence: 60
2025-07-08 22:45:51 | INFO     | trading_agent.ml.ml_strategy:__init__:86 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 22:45:51 | INFO     | trading_agent.ml.ml_strategy:__init__:87 - Components: LSTM + Ensemble + RL + TRANSFORMER + Feature Engineering
2025-07-08 22:45:51 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:45:53 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 1929.98ms
2025-07-08 22:45:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 22:45:53 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:169 - 🚀 Training ML models for 80% win rate...
2025-07-08 22:45:53 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:45:53 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:45:53 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:45:53 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:45:54 | INFO     | trading_agent.ml.feature_engineer:fit_transform:261 - ✅ Features fitted and transformed: (288, 13)
2025-07-08 22:45:54 | INFO     | trading_agent.ml.ensemble_models:prepare_targets:115 - Target distribution: Buy=1, Sell=2, Hold=285
2025-07-08 22:45:54 | WARNING  | trading_agent.ml.lstm_predictor:train:134 - TensorFlow not available! Cannot train LSTM.
2025-07-08 22:45:54 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:184 - LSTM trained: loss=0.000000
2025-07-08 22:45:54 | INFO     | trading_agent.ml.ensemble_models:train:122 - 🚀 Training ensemble for 80% win rate...
2025-07-08 22:45:54 | INFO     | trading_agent.ml.ensemble_models:train:123 - Training data: X (288, 13), y (288,)
2025-07-08 22:45:54 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training random_forest...
2025-07-08 22:45:54 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ random_forest accuracy: 1.0000
2025-07-08 22:45:54 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training xgboost...
2025-07-08 22:45:55 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ xgboost accuracy: 1.0000
2025-07-08 22:45:55 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training lightgbm...
2025-07-08 22:45:55 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ lightgbm accuracy: 1.0000
2025-07-08 22:45:55 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training catboost...
2025-07-08 22:45:59 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ catboost accuracy: 0.9828
2025-07-08 22:45:59 | INFO     | trading_agent.ml.ensemble_models:train:165 - 🎯 Ensemble accuracy: 0.9957
2025-07-08 22:45:59 | INFO     | trading_agent.ml.ensemble_models:train:168 - 🎉 80% WIN RATE ACHIEVED!
2025-07-08 22:45:59 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:189 - Ensemble trained: accuracy=0.9957
2025-07-08 22:45:59 | WARNING  | trading_agent.ml.rl_agent:train:220 - RL not available! Cannot train agent.
2025-07-08 22:45:59 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:194 - RL trained: win_rate=0.0%
2025-07-08 22:45:59 | INFO     | trading_agent.ml.transformer_predictor:train:178 - 🚀 Training Transformer for 80% win rate...
2025-07-08 22:45:59 | INFO     | trading_agent.ml.transformer_predictor:train:179 - Training data: X (288, 13), y (288,)
2025-07-08 22:45:59 | INFO     | trading_agent.ml.transformer_predictor:prepare_sequences:162 - Prepared sequences: X torch.Size([228, 60, 13]), y torch.Size([228, 1])
2025-07-08 22:45:59 | INFO     | trading_agent.ml.transformer_predictor:__init__:87 - 🤖 Transformer model initialized: 128d, 8h, 6L
2025-07-08 22:46:12 | INFO     | trading_agent.ml.transformer_predictor:train:258 - Epoch 0: Train Loss: 0.332630, Val Loss: 0.225457
2025-07-08 22:47:45 | INFO     | trading_agent.ml.transformer_predictor:train:258 - Epoch 10: Train Loss: 0.001614, Val Loss: 0.000124
2025-07-08 22:49:11 | INFO     | trading_agent.ml.transformer_predictor:train:258 - Epoch 20: Train Loss: 0.000642, Val Loss: 0.000021
2025-07-08 22:50:31 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:50:31 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 22:50:31 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 22:50:31 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 22:50:31 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 22:50:31 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 22:50:31 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 22:50:31 | INFO     | trading_agent.ml.ml_strategy:__init__:86 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 22:50:31 | INFO     | trading_agent.ml.ml_strategy:__init__:87 - Components: LSTM + Ensemble + RL + Feature Engineering
2025-07-08 22:50:31 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:50:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 684.95ms
2025-07-08 22:50:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 22:50:32 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:169 - 🚀 Training ML models for 80% win rate...
2025-07-08 22:50:32 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:50:32 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:50:32 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:50:32 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:50:32 | INFO     | trading_agent.ml.feature_engineer:fit_transform:261 - ✅ Features fitted and transformed: (288, 4)
2025-07-08 22:50:32 | INFO     | trading_agent.ml.ensemble_models:prepare_targets:115 - Target distribution: Buy=1, Sell=2, Hold=285
2025-07-08 22:50:32 | WARNING  | trading_agent.ml.lstm_predictor:train:134 - TensorFlow not available! Cannot train LSTM.
2025-07-08 22:50:32 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:184 - LSTM trained: loss=0.000000
2025-07-08 22:50:32 | INFO     | trading_agent.ml.ensemble_models:train:122 - 🚀 Training ensemble for 80% win rate...
2025-07-08 22:50:32 | INFO     | trading_agent.ml.ensemble_models:train:123 - Training data: X (288, 4), y (288,)
2025-07-08 22:50:32 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training random_forest...
2025-07-08 22:50:33 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ random_forest accuracy: 1.0000
2025-07-08 22:50:33 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training xgboost...
2025-07-08 22:50:33 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ xgboost accuracy: 1.0000
2025-07-08 22:50:33 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training lightgbm...
2025-07-08 22:50:33 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ lightgbm accuracy: 1.0000
2025-07-08 22:50:33 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training catboost...
2025-07-08 22:50:34 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ catboost accuracy: 1.0000
2025-07-08 22:50:34 | INFO     | trading_agent.ml.ensemble_models:train:165 - 🎯 Ensemble accuracy: 1.0000
2025-07-08 22:50:34 | INFO     | trading_agent.ml.ensemble_models:train:168 - 🎉 80% WIN RATE ACHIEVED!
2025-07-08 22:50:34 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:189 - Ensemble trained: accuracy=1.0000
2025-07-08 22:50:34 | WARNING  | trading_agent.ml.rl_agent:train:220 - RL not available! Cannot train agent.
2025-07-08 22:50:34 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:194 - RL trained: win_rate=0.0%
2025-07-08 22:50:34 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:202 - ✅ All ML models trained successfully!
2025-07-08 22:50:34 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:50:34 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:50:34 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:50:34 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:50:34 | ERROR    | trading_agent.ml.ml_strategy:_generate_ml_predictions:277 - Error generating ML predictions: 'LSTMPredictor' object has no attribute 'sequence_length'
2025-07-08 22:50:34 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: MLStrategy -> hold
2025-07-08 22:59:31 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:59:31 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 22:59:31 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 22:59:31 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 22:59:31 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 22:59:31 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 22:59:31 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 22:59:31 | INFO     | trading_agent.ml.ml_strategy:__init__:86 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 22:59:31 | INFO     | trading_agent.ml.ml_strategy:__init__:87 - Components: LSTM + Ensemble + RL + Feature Engineering
2025-07-08 22:59:31 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:59:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 738.25ms
2025-07-08 22:59:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 22:59:32 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:169 - 🚀 Training ML models for 80% win rate...
2025-07-08 22:59:32 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:59:32 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:59:32 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:59:32 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:59:32 | INFO     | trading_agent.ml.feature_engineer:fit_transform:261 - ✅ Features fitted and transformed: (288, 5)
2025-07-08 22:59:32 | INFO     | trading_agent.ml.ensemble_models:prepare_targets:115 - Target distribution: Buy=101, Sell=116, Hold=71
2025-07-08 22:59:32 | WARNING  | trading_agent.ml.lstm_predictor:train:134 - TensorFlow not available! Cannot train LSTM.
2025-07-08 22:59:32 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:184 - LSTM trained: loss=0.000000
2025-07-08 22:59:32 | INFO     | trading_agent.ml.ensemble_models:train:122 - 🚀 Training ensemble for 80% win rate...
2025-07-08 22:59:32 | INFO     | trading_agent.ml.ensemble_models:train:123 - Training data: X (288, 5), y (288,)
2025-07-08 22:59:32 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training random_forest...
2025-07-08 22:59:33 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ random_forest accuracy: 0.3103
2025-07-08 22:59:33 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training xgboost...
2025-07-08 22:59:33 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ xgboost accuracy: 0.2586
2025-07-08 22:59:33 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training lightgbm...
2025-07-08 22:59:33 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ lightgbm accuracy: 0.3103
2025-07-08 22:59:33 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training catboost...
2025-07-08 22:59:34 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ catboost accuracy: 0.2931
2025-07-08 22:59:35 | INFO     | trading_agent.ml.ensemble_models:train:165 - 🎯 Ensemble accuracy: 0.2931
2025-07-08 22:59:35 | INFO     | trading_agent.ml.ensemble_models:train:170 - 📈 Need 0.5069 more for 80% target
2025-07-08 22:59:35 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:189 - Ensemble trained: accuracy=0.2931
2025-07-08 22:59:35 | WARNING  | trading_agent.ml.rl_agent:train:220 - RL not available! Cannot train agent.
2025-07-08 22:59:35 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:194 - RL trained: win_rate=0.0%
2025-07-08 22:59:35 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:202 - ✅ All ML models trained successfully!
2025-07-08 22:59:35 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:59:35 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:59:35 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:59:35 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:59:35 | ERROR    | trading_agent.ml.ml_strategy:_generate_ml_predictions:277 - Error generating ML predictions: 'LSTMPredictor' object has no attribute 'sequence_length'
2025-07-08 22:59:35 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: MLStrategy -> hold
2025-07-08 23:00:41 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 23:00:41 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 23:00:41 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 23:00:41 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 23:00:41 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 23:00:41 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 23:00:41 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 23:00:41 | INFO     | trading_agent.ml.ml_strategy:__init__:86 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 23:00:41 | INFO     | trading_agent.ml.ml_strategy:__init__:87 - Components: LSTM + Ensemble + RL + Feature Engineering
2025-07-08 23:00:41 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:00:42 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 843.20ms
2025-07-08 23:00:42 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 23:00:42 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:169 - 🚀 Training ML models for 80% win rate...
2025-07-08 23:00:42 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 23:00:42 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 23:00:42 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 23:00:42 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 23:00:42 | INFO     | trading_agent.ml.feature_engineer:fit_transform:261 - ✅ Features fitted and transformed: (288, 4)
2025-07-08 23:00:42 | INFO     | trading_agent.ml.ensemble_models:prepare_targets:115 - Target distribution: Buy=101, Sell=116, Hold=71
2025-07-08 23:00:42 | WARNING  | trading_agent.ml.lstm_predictor:train:134 - TensorFlow not available! Cannot train LSTM.
2025-07-08 23:00:42 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:184 - LSTM trained: loss=0.000000
2025-07-08 23:00:42 | INFO     | trading_agent.ml.ensemble_models:train:122 - 🚀 Training ensemble for 80% win rate...
2025-07-08 23:00:42 | INFO     | trading_agent.ml.ensemble_models:train:123 - Training data: X (288, 4), y (288,)
2025-07-08 23:00:42 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training random_forest...
2025-07-08 23:00:43 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ random_forest accuracy: 0.2931
2025-07-08 23:00:43 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training xgboost...
2025-07-08 23:00:43 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ xgboost accuracy: 0.3103
2025-07-08 23:00:43 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training lightgbm...
2025-07-08 23:00:43 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ lightgbm accuracy: 0.2069
2025-07-08 23:00:43 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training catboost...
2025-07-08 23:00:44 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ catboost accuracy: 0.3448
2025-07-08 23:00:44 | INFO     | trading_agent.ml.ensemble_models:train:165 - 🎯 Ensemble accuracy: 0.2888
2025-07-08 23:00:44 | INFO     | trading_agent.ml.ensemble_models:train:170 - 📈 Need 0.5112 more for 80% target
2025-07-08 23:00:44 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:189 - Ensemble trained: accuracy=0.2888
2025-07-08 23:00:44 | WARNING  | trading_agent.ml.rl_agent:train:220 - RL not available! Cannot train agent.
2025-07-08 23:00:44 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:194 - RL trained: win_rate=0.0%
2025-07-08 23:00:44 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:202 - ✅ All ML models trained successfully!
2025-07-08 23:00:44 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 23:00:44 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 23:00:44 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 23:00:45 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 23:00:45 | WARNING  | trading_agent.ml.ml_strategy:_generate_ml_predictions:239 - LSTM prediction error: 'LSTMPredictor' object has no attribute 'sequence_length'
2025-07-08 23:00:45 | ERROR    | trading_agent.ml.ml_strategy:_generate_ml_predictions:282 - Error generating ML predictions: setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (4, 1) + inhomogeneous part.
2025-07-08 23:00:45 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: MLStrategy -> hold
2025-07-08 23:00:55 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 23:00:56 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 23:00:56 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 23:00:56 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 23:00:56 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 23:00:56 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with ml strategy
2025-07-08 23:00:56 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:00:57 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 593.80ms
2025-07-08 23:00:57 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:00:57 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 23:00:57 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 23:00:57 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:00:57 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:02 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:02 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.35ms
2025-07-08 23:01:02 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:02 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:02 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:02 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:02 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:07 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:07 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.19ms
2025-07-08 23:01:07 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:07 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:07 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:07 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:07 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:12 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:12 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.28ms
2025-07-08 23:01:12 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:12 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:12 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:12 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:12 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:17 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:17 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.38ms
2025-07-08 23:01:17 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:17 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:17 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:17 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:17 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:22 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.46ms
2025-07-08 23:01:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:22 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:22 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:22 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:22 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:27 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.94ms
2025-07-08 23:01:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:27 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:27 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:27 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:27 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.89ms
2025-07-08 23:01:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:32 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:32 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:32 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:32 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:37 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.38ms
2025-07-08 23:01:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:37 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:37 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:37 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:37 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:42 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:43 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.25ms
2025-07-08 23:01:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:43 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:43 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:43 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:43 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:48 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:48 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.33ms
2025-07-08 23:01:48 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:48 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:48 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:48 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:48 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:53 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.00ms
2025-07-08 23:01:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:53 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:53 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:53 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:53 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:01:58 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:01:58 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.45ms
2025-07-08 23:01:58 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:01:58 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:58 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:01:58 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:01:58 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:03 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:03 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 5.95ms
2025-07-08 23:02:03 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:03 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:03 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:03 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:03 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.83ms
2025-07-08 23:02:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:08 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:08 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:08 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:08 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:13 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.20ms
2025-07-08 23:02:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:13 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:13 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:13 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:13 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:18 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:18 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.74ms
2025-07-08 23:02:18 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:18 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:18 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:18 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:18 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:23 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.12ms
2025-07-08 23:02:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:23 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:23 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:23 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:23 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:28 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:28 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.30ms
2025-07-08 23:02:28 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:28 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:28 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:28 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:28 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:33 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:33 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.90ms
2025-07-08 23:02:33 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:33 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:33 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:33 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:33 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:38 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:38 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.71ms
2025-07-08 23:02:38 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:38 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:38 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:38 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:38 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:43 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.02ms
2025-07-08 23:02:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:43 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:43 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:43 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:43 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:48 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:48 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.32ms
2025-07-08 23:02:48 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:48 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:48 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:48 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:48 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:53 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.09ms
2025-07-08 23:02:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:53 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:53 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:53 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:53 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:02:58 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:02:58 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.22ms
2025-07-08 23:02:58 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:02:58 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:58 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:02:58 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:02:58 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:03:03 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:03:03 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.81ms
2025-07-08 23:03:03 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:03:03 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:03:03 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:03:03 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:03:03 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:03:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:03:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.56ms
2025-07-08 23:03:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 23:03:08 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 23:03:08 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 23:03:08 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 23:03:08 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 23:12:58 | INFO     | __main__:__init__:29 - 🚀 Alpha Vantage fetcher initialized with API key: demo...
2025-07-08 23:12:58 | INFO     | __main__:fetch_forex_data:47 - Fetching EUR/USD data from Alpha Vantage...
2025-07-08 23:12:58 | ERROR    | __main__:fetch_forex_data:111 - Error fetching Alpha Vantage data: log_api_call() got an unexpected keyword argument 'service'
2025-07-08 23:12:59 | ERROR    | __main__:fetch_latest_price:180 - Error fetching latest Alpha Vantage price: log_api_call() got an unexpected keyword argument 'service'
2025-07-08 23:12:59 | INFO     | __main__:close:186 - Alpha Vantage client closed
2025-07-08 23:13:36 | INFO     | trading_agent.data.alpha_vantage_fetcher:__init__:29 - 🚀 Alpha Vantage fetcher initialized with API key: demo...
2025-07-08 23:13:36 | INFO     | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:47 - Fetching EUR/USD data from Alpha Vantage...
2025-07-08 23:13:37 | ERROR    | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:111 - Error fetching Alpha Vantage data: log_api_call() got an unexpected keyword argument 'service'
2025-07-08 23:13:37 | ERROR    | trading_agent.data.alpha_vantage_fetcher:fetch_latest_price:180 - Error fetching latest Alpha Vantage price: log_api_call() got an unexpected keyword argument 'service'
2025-07-08 23:13:37 | INFO     | trading_agent.data.alpha_vantage_fetcher:close:186 - Alpha Vantage client closed
2025-07-08 23:14:24 | INFO     | trading_agent.data.alpha_vantage_fetcher:__init__:29 - 🚀 Alpha Vantage fetcher initialized with API key: demo...
2025-07-08 23:14:24 | INFO     | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:47 - Fetching EUR/USD data from Alpha Vantage...
2025-07-08 23:14:25 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Alpha Vantage [200] in 480.68ms
2025-07-08 23:14:25 | ERROR    | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:78 - No time series data found. Keys: ['Information']
2025-07-08 23:14:25 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Alpha Vantage Real-time [200] in 338.19ms
2025-07-08 23:14:25 | ERROR    | trading_agent.data.alpha_vantage_fetcher:fetch_latest_price:150 - No real-time data found. Keys: ['Information']
2025-07-08 23:14:25 | INFO     | trading_agent.data.alpha_vantage_fetcher:close:186 - Alpha Vantage client closed
2025-07-08 23:15:07 | INFO     | trading_agent.data.alpha_vantage_fetcher:__init__:29 - 🚀 Alpha Vantage fetcher initialized with API key: demo...
2025-07-08 23:15:07 | INFO     | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:47 - Fetching EUR/USD data from Alpha Vantage...
2025-07-08 23:15:07 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Alpha Vantage [200] in 421.86ms
2025-07-08 23:15:08 | INFO     | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:76 - Alpha Vantage response keys: ['Information']
2025-07-08 23:15:08 | ERROR    | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:86 - No time series data found. Keys: ['Information']
2025-07-08 23:15:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Alpha Vantage Real-time [200] in 144.94ms
2025-07-08 23:15:08 | ERROR    | trading_agent.data.alpha_vantage_fetcher:fetch_latest_price:158 - No real-time data found. Keys: ['Information']
2025-07-08 23:15:08 | INFO     | trading_agent.data.alpha_vantage_fetcher:close:194 - Alpha Vantage client closed
2025-07-08 23:15:39 | INFO     | trading_agent.data.alpha_vantage_fetcher:__init__:29 - 🚀 Alpha Vantage fetcher initialized with API key: demo...
2025-07-08 23:15:39 | INFO     | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:47 - Fetching EUR/USD data from Alpha Vantage...
2025-07-08 23:15:39 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Alpha Vantage [200] in 185.11ms
2025-07-08 23:15:39 | INFO     | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:67 - Alpha Vantage full response: {'Information': 'The **demo** API key is for demo purposes only. Please claim your free API key at (https://www.alphavantage.co/support/#api-key) to explore our full API offerings. It takes fewer than 20 seconds.'}
2025-07-08 23:15:39 | WARNING  | trading_agent.data.alpha_vantage_fetcher:fetch_forex_data:79 - Only Information key found: The **demo** API key is for demo purposes only. Please claim your free API key at (https://www.alphavantage.co/support/#api-key) to explore our full API offerings. It takes fewer than 20 seconds.
2025-07-08 23:15:39 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Alpha Vantage Real-time [200] in 336.80ms
2025-07-08 23:15:39 | ERROR    | trading_agent.data.alpha_vantage_fetcher:fetch_latest_price:165 - No real-time data found. Keys: ['Information']
2025-07-08 23:15:39 | INFO     | trading_agent.data.alpha_vantage_fetcher:close:201 - Alpha Vantage client closed
2025-07-08 23:17:15 | INFO     | __main__:__init__:29 - 🚀 Mock data generator initialized at 1.172
2025-07-08 23:17:15 | INFO     | __main__:generate_historical_data:103 - ✅ Generated 24 historical bars
2025-07-08 23:17:28 | INFO     | trading_agent.data.alpha_vantage_fetcher:__init__:29 - 🚀 Alpha Vantage fetcher initialized with API key: demo...
2025-07-08 23:17:28 | INFO     | trading_agent.data.fetcher:__init__:51 - 🚀 DataFetcher initialized with AlphaVantageDataFetcher for ZACHRANA LUDSTVA!
2025-07-08 23:17:29 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 23:17:29 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 23:17:29 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 23:17:29 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 23:17:29 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 23:17:29 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 23:17:29 | INFO     | trading_agent.ml.ml_strategy:__init__:86 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 23:17:29 | INFO     | trading_agent.ml.ml_strategy:__init__:87 - Components: LSTM + Ensemble + RL + Feature Engineering
2025-07-08 23:17:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Fetching EUR/USD data from Alpha Vantage
2025-07-08 23:17:29 | WARNING  | trading_agent.data.fetcher:fetch_market_data:99 - Failed to fetch from Alpha Vantage: Alpha Vantage not available in minimal version
2025-07-08 23:17:29 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Alpha Vantage [500] in 0.00ms
2025-07-08 23:17:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 23:17:29 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 931.39ms
2025-07-08 23:17:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:95 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 23:17:29 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:169 - 🚀 Training ML models for 80% win rate...
2025-07-08 23:17:30 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 23:17:30 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 23:17:30 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 23:17:30 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 23:17:30 | INFO     | trading_agent.ml.feature_engineer:fit_transform:261 - ✅ Features fitted and transformed: (288, 14)
2025-07-08 23:17:30 | INFO     | trading_agent.ml.ensemble_models:prepare_targets:115 - Target distribution: Buy=101, Sell=116, Hold=71
2025-07-08 23:17:30 | WARNING  | trading_agent.ml.lstm_predictor:train:134 - TensorFlow not available! Cannot train LSTM.
2025-07-08 23:17:30 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:184 - LSTM trained: loss=0.000000
2025-07-08 23:17:30 | INFO     | trading_agent.ml.ensemble_models:train:122 - 🚀 Training ensemble for 80% win rate...
2025-07-08 23:17:30 | INFO     | trading_agent.ml.ensemble_models:train:123 - Training data: X (288, 14), y (288,)
2025-07-08 23:17:30 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training random_forest...
2025-07-08 23:17:30 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ random_forest accuracy: 0.3793
2025-07-08 23:17:30 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training xgboost...
2025-07-08 23:17:31 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ xgboost accuracy: 0.2759
2025-07-08 23:17:31 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training lightgbm...
2025-07-08 23:17:31 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ lightgbm accuracy: 0.2414
2025-07-08 23:17:31 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training catboost...
2025-07-08 23:17:33 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ catboost accuracy: 0.3276
2025-07-08 23:17:33 | INFO     | trading_agent.ml.ensemble_models:train:165 - 🎯 Ensemble accuracy: 0.3060
2025-07-08 23:17:33 | INFO     | trading_agent.ml.ensemble_models:train:170 - 📈 Need 0.4940 more for 80% target
2025-07-08 23:17:33 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:189 - Ensemble trained: accuracy=0.3060
2025-07-08 23:17:33 | WARNING  | trading_agent.ml.rl_agent:train:220 - RL not available! Cannot train agent.
2025-07-08 23:17:33 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:194 - RL trained: win_rate=0.0%
2025-07-08 23:17:33 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:202 - ✅ All ML models trained successfully!
2025-07-08 23:17:33 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 23:17:33 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 23:17:33 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 23:17:33 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 23:17:33 | WARNING  | trading_agent.ml.ml_strategy:_generate_ml_predictions:239 - LSTM prediction error: 'LSTMPredictor' object has no attribute 'sequence_length'
2025-07-08 23:17:33 | ERROR    | trading_agent.ml.ml_strategy:_generate_ml_predictions:282 - Error generating ML predictions: setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (4, 1) + inhomogeneous part.
2025-07-08 23:17:33 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: MLStrategy -> hold
