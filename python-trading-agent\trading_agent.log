2025-07-08 20:47:22 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 20:47:23 | INFO     | trading_agent.data.fetcher:__init__:43 - <PERSON><PERSON><PERSON><PERSON> initialized with async HTTP client
2025-07-08 20:47:23 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 20:47:23 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 20:47:23 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 20:47:23 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 20:47:23 | INFO     | __main__:__init__:33 - Simple Trading Server initialized
2025-07-08 20:47:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 20:47:27 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 4736.68ms
2025-07-08 20:47:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 20:47:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 20:47:28 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 168.94ms
2025-07-08 20:47:28 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 20:47:28 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: SmartStrategy -> hold
2025-07-08 20:51:05 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 20:51:06 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 20:51:06 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 20:51:06 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 20:51:06 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 20:51:06 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 20:51:06 | INFO     | __main__:__init__:33 - Simple Trading Server initialized
2025-07-08 20:51:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 20:51:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 2156.98ms
2025-07-08 20:51:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 20:51:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 20:51:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 131.44ms
2025-07-08 20:51:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 20:51:08 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: SmartStrategy -> hold
2025-07-08 21:11:36 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:11:36 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:11:36 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:11:36 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:11:36 | ERROR    | trading_agent.cli:run_challenge:388 - Challenge error: Can't instantiate abstract class UltraStrategy without an implementation for abstract method 'decide_action'
2025-07-08 21:12:16 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:12:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:12:16 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 696.89ms
2025-07-08 21:12:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 21:12:16 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:12:16 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:12:16 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:12:16 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:12:16 | ERROR    | trading_agent.cli:run_tests:427 - Test error: Can't instantiate abstract class UltraStrategy without an implementation for abstract method 'decide_action'
2025-07-08 21:15:50 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:15:50 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:15:51 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 1044.60ms
2025-07-08 21:15:51 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy UltraStrategy initialized
2025-07-08 21:15:51 | INFO     | trading_agent.strategies.ultra_strategy:__init__:69 - UltraStrategy initialized for 80%+ win rate
2025-07-08 21:15:51 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:28:05 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:28:05 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:28:10 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 5170.26ms
2025-07-08 21:28:10 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 1 data points from Yahoo Finance
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy UltraStrategy initialized
2025-07-08 21:28:10 | INFO     | trading_agent.strategies.ultra_strategy:__init__:69 - UltraStrategy initialized for 80%+ win rate
2025-07-08 21:28:10 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy OptimizedStrategy initialized
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.optimized_strategy:__init__:60 - OptimizedStrategy initialized with advanced features
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy UltraStrategy initialized
2025-07-08 21:28:29 | INFO     | trading_agent.strategies.ultra_strategy:__init__:69 - UltraStrategy initialized for 80%+ win rate
2025-07-08 21:28:30 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:28:30 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:28:30 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:28:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 1457.81ms
2025-07-08 21:28:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:28:32 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:28:32 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:28:32 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:28:32 | ERROR    | trading_agent.cli:run_challenge:388 - Challenge error: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:29:00 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy UltraStrategy initialized
2025-07-08 21:29:00 | INFO     | trading_agent.strategies.ultra_strategy:__init__:69 - UltraStrategy initialized for 80%+ win rate
2025-07-08 21:29:01 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:29:01 | ERROR    | trading_agent.cli:run_backtest:265 - Backtest error: 'Command' object has no attribute 'initial_balance'
2025-07-08 21:29:32 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:29:32 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:29:32 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:29:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:29:33 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 716.12ms
2025-07-08 21:29:33 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 21:29:33 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: SmartStrategy -> hold
2025-07-08 21:29:54 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:29:54 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:29:54 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:29:54 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:29:55 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:29:55 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with smart strategy
2025-07-08 21:29:55 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:29:56 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 714.70ms
2025-07-08 21:29:56 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:29:56 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:29:56 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:29:56 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:29:56 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:01 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:01 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.38ms
2025-07-08 21:30:01 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:01 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:01 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:01 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:01 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:06 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.71ms
2025-07-08 21:30:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:06 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:06 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:06 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:06 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:11 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:11 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.94ms
2025-07-08 21:30:11 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:11 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:11 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:11 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:11 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:16 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.44ms
2025-07-08 21:30:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:16 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:16 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:16 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:17 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:22 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.26ms
2025-07-08 21:30:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:22 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:22 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:22 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:22 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:27 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.52ms
2025-07-08 21:30:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:27 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:27 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:27 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:27 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.43ms
2025-07-08 21:30:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:32 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:32 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:32 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:32 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:37 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.15ms
2025-07-08 21:30:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:37 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:37 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:37 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:37 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:42 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:42 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.00ms
2025-07-08 21:30:42 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:42 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:42 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:42 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:42 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:47 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:47 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.59ms
2025-07-08 21:30:47 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:47 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:47 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:47 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:47 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:30:52 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:30:52 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.66ms
2025-07-08 21:30:52 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:30:52 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:52 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:30:52 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:30:52 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:45:14 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:45:14 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy ScalpingStrategy initialized
2025-07-08 21:45:14 | INFO     | trading_agent.strategies.scalping_strategy:__init__:70 - ScalpingStrategy initialized - 83% win rate proven method
2025-07-08 21:45:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:45:16 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 2017.25ms
2025-07-08 21:45:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 21:45:16 | ERROR    | trading_agent.cli:run_analysis:100 - Analysis error: 'ScalpingStrategy' object has no attribute '_analyze_scalping_market_condition'
2025-07-08 21:48:31 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:48:31 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy ScalpingStrategy initialized
2025-07-08 21:48:31 | INFO     | trading_agent.strategies.scalping_strategy:__init__:70 - ScalpingStrategy initialized - 83% win rate proven method
2025-07-08 21:48:31 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:48:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 974.88ms
2025-07-08 21:48:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 21:48:32 | ERROR    | trading_agent.cli:run_analysis:100 - Analysis error: 1 validation error for ICTSetup
entry_zone
  Input should be a valid dictionary [type=dict_type, input_value=1.1739845275878906, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/dict_type
2025-07-08 21:50:13 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:50:13 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy ScalpingStrategy initialized
2025-07-08 21:50:13 | INFO     | trading_agent.strategies.scalping_strategy:__init__:70 - ScalpingStrategy initialized - 83% win rate proven method
2025-07-08 21:50:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:14 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 810.15ms
2025-07-08 21:50:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 21:50:14 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: ScalpingStrategy -> hold
2025-07-08 21:50:38 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 21:50:39 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:50:39 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 21:50:39 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 21:50:40 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 21:50:40 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with scalping strategy
2025-07-08 21:50:40 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:41 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 742.46ms
2025-07-08 21:50:41 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:50:41 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:50:41 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 21:50:41 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:50:41 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:50:46 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:46 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.26ms
2025-07-08 21:50:46 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:50:46 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:46 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:46 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:50:46 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:50:51 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:51 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.41ms
2025-07-08 21:50:51 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:50:51 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:51 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:51 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:50:51 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:50:56 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:50:56 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.22ms
2025-07-08 21:50:56 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:50:56 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:56 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:50:56 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:50:56 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:01 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:01 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.65ms
2025-07-08 21:51:01 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:01 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:01 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:01 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:01 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:06 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.34ms
2025-07-08 21:51:06 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:06 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:06 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:06 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:06 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:11 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:11 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.73ms
2025-07-08 21:51:11 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:11 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:11 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:11 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:11 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:16 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.14ms
2025-07-08 21:51:16 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:16 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:16 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:16 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:16 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:21 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:21 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.21ms
2025-07-08 21:51:21 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:21 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:21 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:21 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:21 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:26 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:26 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.38ms
2025-07-08 21:51:26 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:26 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:26 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:26 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:26 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:31 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:31 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.15ms
2025-07-08 21:51:31 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:31 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:31 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:31 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:31 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:36 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:36 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.93ms
2025-07-08 21:51:36 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:36 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:36 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:36 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:36 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:41 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:41 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.63ms
2025-07-08 21:51:41 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:41 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:42 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:42 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:42 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:47 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:47 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.10ms
2025-07-08 21:51:47 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:47 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:47 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:47 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:47 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:52 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:52 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.87ms
2025-07-08 21:51:52 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:52 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:52 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:52 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:52 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:51:57 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:51:57 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.00ms
2025-07-08 21:51:57 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:51:57 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:57 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:51:57 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:51:57 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:02 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:02 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.36ms
2025-07-08 21:52:02 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:02 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:02 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:02 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:02 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:07 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:07 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.53ms
2025-07-08 21:52:07 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:07 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:07 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:07 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:07 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:12 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:12 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.53ms
2025-07-08 21:52:12 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:12 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:12 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:12 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:12 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:17 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:17 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.02ms
2025-07-08 21:52:17 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:17 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:17 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:17 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:17 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:22 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.11ms
2025-07-08 21:52:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:22 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:22 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:22 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:22 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:27 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.65ms
2025-07-08 21:52:27 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:27 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:27 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:27 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:27 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:32 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.77ms
2025-07-08 21:52:32 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:32 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:32 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:32 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:32 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 21:52:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 21:52:37 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.80ms
2025-07-08 21:52:37 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 21:52:37 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:37 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 21:52:37 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 21:52:37 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:21:21 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:21:21 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 22:21:21 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 22:21:21 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 22:21:21 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 22:21:21 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 22:21:21 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 22:21:21 | INFO     | trading_agent.ml.ml_strategy:__init__:83 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 22:21:21 | INFO     | trading_agent.ml.ml_strategy:__init__:84 - Components: LSTM + Ensemble + RL + Feature Engineering
2025-07-08 22:21:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:21:22 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 695.16ms
2025-07-08 22:21:22 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 22:21:22 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:166 - 🚀 Training ML models for 80% win rate...
2025-07-08 22:21:22 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:21:22 | ERROR    | trading_agent.ml.ml_strategy:_train_ml_models:197 - Error training ML models: aroon_up() missing 1 required positional argument: 'low'
2025-07-08 22:21:22 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: MLStrategy -> hold
2025-07-08 22:22:06 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 22:22:07 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:22:07 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 22:22:07 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 22:22:08 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:22:08 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with ml strategy
2025-07-08 22:22:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 718.01ms
2025-07-08 22:22:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:09 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:22:09 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:22:09 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:09 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:19 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.82ms
2025-07-08 22:22:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:19 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:19 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:19 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:19 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:29 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.43ms
2025-07-08 22:22:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:29 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:29 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:29 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:29 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:39 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.22ms
2025-07-08 22:22:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:39 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:39 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:39 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:39 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:49 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.78ms
2025-07-08 22:22:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:49 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:49 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:49 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:49 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:22:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:22:59 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 28.22ms
2025-07-08 22:22:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:22:59 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:59 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:22:59 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:22:59 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:09 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:09 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.07ms
2025-07-08 22:23:09 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:09 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:09 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:09 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:09 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:19 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.40ms
2025-07-08 22:23:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:19 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:19 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:19 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:19 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:29 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.90ms
2025-07-08 22:23:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:29 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:29 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:29 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:29 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:39 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.43ms
2025-07-08 22:23:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:39 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:39 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:39 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:39 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:49 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.49ms
2025-07-08 22:23:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:49 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:49 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:49 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:49 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:23:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:23:59 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.47ms
2025-07-08 22:23:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:23:59 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:59 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:23:59 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:23:59 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:09 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:09 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.63ms
2025-07-08 22:24:09 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:09 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:09 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:09 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:09 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:19 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.40ms
2025-07-08 22:24:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:19 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:19 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:20 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:20 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:30 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:30 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.01ms
2025-07-08 22:24:30 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:30 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:30 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:30 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:30 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:40 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:40 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.76ms
2025-07-08 22:24:40 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:40 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:40 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:40 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:40 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:24:50 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:24:50 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.42ms
2025-07-08 22:24:50 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:24:50 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:50 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:24:50 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:24:50 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:25:00 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:25:00 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.37ms
2025-07-08 22:25:00 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:25:00 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:00 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:00 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:25:00 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:25:10 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:25:10 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.30ms
2025-07-08 22:25:10 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:25:10 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:10 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:10 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:25:10 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:25:20 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:25:20 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 120.66ms
2025-07-08 22:25:20 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:25:20 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:20 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:25:20 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:25:20 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:25:43 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:25:43 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy MLStrategy initialized
2025-07-08 22:25:43 | INFO     | trading_agent.ml.feature_engineer:__init__:30 - 🚀 FeatureEngineer initialized for 80% win rate!
2025-07-08 22:25:43 | WARNING  | trading_agent.ml.lstm_predictor:__init__:39 - TensorFlow not available! Using dummy predictor.
2025-07-08 22:25:43 | INFO     | trading_agent.ml.ensemble_models:__init__:97 - 🚀 Ensemble initialized with 4 models!
2025-07-08 22:25:43 | INFO     | trading_agent.ml.ensemble_models:__init__:98 - Available models: ['random_forest', 'xgboost', 'lightgbm', 'catboost']
2025-07-08 22:25:43 | WARNING  | trading_agent.ml.rl_agent:__init__:197 - RL libraries not available! Using dummy agent.
2025-07-08 22:25:43 | INFO     | trading_agent.ml.ml_strategy:__init__:83 - 🚀 ML Strategy initialized - ZACHRANA LUDSTVA!
2025-07-08 22:25:43 | INFO     | trading_agent.ml.ml_strategy:__init__:84 - Components: LSTM + Ensemble + RL + Feature Engineering
2025-07-08 22:25:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:25:44 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 833.42ms
2025-07-08 22:25:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:86 - Successfully fetched 288 data points from Yahoo Finance
2025-07-08 22:25:44 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:166 - 🚀 Training ML models for 80% win rate...
2025-07-08 22:25:44 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:25:45 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:25:45 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:25:45 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:25:45 | INFO     | trading_agent.ml.feature_engineer:fit_transform:261 - ✅ Features fitted and transformed: (288, 9)
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ensemble_models:prepare_targets:115 - Target distribution: Buy=1, Sell=2, Hold=285
2025-07-08 22:25:45 | WARNING  | trading_agent.ml.lstm_predictor:train:134 - TensorFlow not available! Cannot train LSTM.
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:181 - LSTM trained: loss=0.000000
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ensemble_models:train:122 - 🚀 Training ensemble for 80% win rate...
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ensemble_models:train:123 - Training data: X (288, 9), y (288,)
2025-07-08 22:25:45 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training random_forest...
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ random_forest accuracy: 1.0000
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training xgboost...
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ xgboost accuracy: 1.0000
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training lightgbm...
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ lightgbm accuracy: 0.9828
2025-07-08 22:25:46 | INFO     | trading_agent.ml.ensemble_models:train:139 - Training catboost...
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ensemble_models:train:154 - ✅ catboost accuracy: 1.0000
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ensemble_models:train:165 - 🎯 Ensemble accuracy: 0.9957
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ensemble_models:train:168 - 🎉 80% WIN RATE ACHIEVED!
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:186 - Ensemble trained: accuracy=0.9957
2025-07-08 22:25:50 | WARNING  | trading_agent.ml.rl_agent:train:220 - RL not available! Cannot train agent.
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:191 - RL trained: win_rate=0.0%
2025-07-08 22:25:50 | INFO     | trading_agent.ml.ml_strategy:_train_ml_models:194 - ✅ All ML models trained successfully!
2025-07-08 22:25:50 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:25:50 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:25:50 | INFO     | trading_agent.ml.feature_engineer:engineer_features:34 - Engineering features from 288 data points...
2025-07-08 22:25:50 | INFO     | trading_agent.ml.feature_engineer:engineer_features:65 - ✅ Created 104 features!
2025-07-08 22:25:50 | ERROR    | trading_agent.ml.ml_strategy:_generate_ml_predictions:259 - Error generating ML predictions: 'LSTMPredictor' object has no attribute 'sequence_length'
2025-07-08 22:25:50 | INFO     | trading_agent.utils.logger:strategy_signal:80 - Strategy signal: MLStrategy -> hold
2025-07-08 22:26:11 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 22:26:11 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:26:11 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 22:26:11 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 22:26:12 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:26:12 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with ml strategy
2025-07-08 22:26:12 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:13 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 643.40ms
2025-07-08 22:26:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:13 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:26:13 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:26:13 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:13 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:26:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:23 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 8.86ms
2025-07-08 22:26:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:23 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:23 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:23 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:23 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:26:33 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:33 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.27ms
2025-07-08 22:26:33 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:33 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:33 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:33 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:33 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:26:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:43 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.28ms
2025-07-08 22:26:43 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:43 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:43 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:43 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:43 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:26:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:26:53 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.27ms
2025-07-08 22:26:53 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:26:53 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:53 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:26:53 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:26:53 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:03 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:03 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.28ms
2025-07-08 22:27:03 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:03 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:03 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:03 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:03 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:13 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 9.22ms
2025-07-08 22:27:13 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:13 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:13 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:13 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:13 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:23 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.21ms
2025-07-08 22:27:23 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:23 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:24 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:24 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:24 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:34 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:34 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.81ms
2025-07-08 22:27:34 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:34 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:34 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:34 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:34 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:44 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.50ms
2025-07-08 22:27:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:44 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:44 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:44 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:44 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:27:54 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:27:54 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.53ms
2025-07-08 22:27:54 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:27:54 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:54 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:27:54 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:27:54 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:28:04 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:28:04 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.16ms
2025-07-08 22:28:04 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:28:04 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:28:04 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:28:04 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:28:04 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:28:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:28:14 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 7.87ms
2025-07-08 22:28:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:28:14 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:28:14 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:28:14 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:28:14 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:06 | INFO     | trading_agent.core.trader:__init__:41 - Trader initialized with balance: $10,000.00
2025-07-08 22:32:07 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:32:07 | INFO     | trading_agent.strategies.base_strategy:__init__:46 - Strategy SmartStrategy initialized
2025-07-08 22:32:07 | INFO     | trading_agent.strategies.smart_strategy:__init__:42 - SmartStrategy initialized with config: SmartStrategy
2025-07-08 22:32:07 | INFO     | trading_agent.data.fetcher:__init__:43 - DataFetcher initialized with async HTTP client
2025-07-08 22:32:07 | INFO     | trading_agent.simulator.live_trader:__init__:54 - Live Trading Simulator initialized with ml strategy
2025-07-08 22:32:07 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:08 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 731.07ms
2025-07-08 22:32:08 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:09 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:32:09 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: API error: {'code': 101, 'type': 'missing_access_key', 'info': 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]'}
2025-07-08 22:32:09 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:09 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:14 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.27ms
2025-07-08 22:32:14 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:14 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:14 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:14 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:14 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:19 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.27ms
2025-07-08 22:32:19 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:19 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:19 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:19 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:19 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:24 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:24 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.51ms
2025-07-08 22:32:24 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:24 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:24 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:24 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:24 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:29 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 5.83ms
2025-07-08 22:32:29 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:29 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:29 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:29 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:29 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:34 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:34 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.18ms
2025-07-08 22:32:34 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:34 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:34 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:34 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:34 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:39 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.59ms
2025-07-08 22:32:39 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:39 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:39 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:39 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:39 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:44 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.53ms
2025-07-08 22:32:44 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:44 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:44 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:44 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:44 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:49 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.00ms
2025-07-08 22:32:49 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:49 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:49 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:49 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:49 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:54 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:54 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 5.96ms
2025-07-08 22:32:54 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:54 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:54 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:54 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:54 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:32:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:32:59 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 6.53ms
2025-07-08 22:32:59 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:32:59 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:59 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:32:59 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:32:59 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
2025-07-08 22:33:04 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Yahoo Finance
2025-07-08 22:33:04 | INFO     | trading_agent.utils.logger:api_call:129 - API call: Yahoo Finance [200] in 16.38ms
2025-07-08 22:33:04 | INFO     | trading_agent.data.fetcher:fetch_market_data:77 - Fetching EUR/USD data from Exchange Rate Host
2025-07-08 22:33:04 | ERROR    | trading_agent.data.fetcher:_fetch_exchange_rate_host:229 - Exchange Rate Host fetch error: Cannot send a request, as the client has been closed.
2025-07-08 22:33:04 | WARNING  | trading_agent.data.fetcher:fetch_market_data:90 - Failed to fetch from Exchange Rate Host: Exchange Rate Host error: Cannot send a request, as the client has been closed.
2025-07-08 22:33:04 | ERROR    | trading_agent.utils.logger:api_call:129 - API call: Exchange Rate Host [500] in 0.00ms
2025-07-08 22:33:04 | ERROR    | trading_agent.simulator.live_trader:_process_market_tick:150 - Error processing market tick: Failed to fetch data for EUR/USD from all sources
