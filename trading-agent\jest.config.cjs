module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  transform: {
    '^.+\\.ts$': ['ts-jest', { useESM: true }],
  },
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
    '^@modelcontextprotocol/sdk/client/index.js$': '<rootDir>/node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js',
    '^@modelcontextprotocol/sdk/client/stdio.js$': '<rootDir>/node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js',
    '^@modelcontextprotocol/sdk(.*)$': '<rootDir>/node_modules/@modelcontextprotocol/sdk/dist/esm$1',
  },
  moduleFileExtensions: ['js', 'json', 'ts', 'node', 'mjs'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/index.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testTimeout: 30000, // 30 seconds for evolution tests
  maxWorkers: 4, // Limit workers for resource-intensive evolution tests
  verbose: true
};