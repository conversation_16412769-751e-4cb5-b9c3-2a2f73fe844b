// src/agent/optimizedSmartStrategy.ts

import { TradingStrategy, TradeOrder } from './strategy.js';
import { TechnicalIndicators, IndicatorResult } from '../indicators/technicalIndicators.js';

export interface OptimizedMarketCondition {
    trend: 'uptrend' | 'downtrend' | 'sideways';
    trendStrength: number; // 0-100
    volatility: 'low' | 'medium' | 'high';
    momentum: 'strong' | 'weak' | 'neutral';
    volume: 'high' | 'low' | 'normal';
    marketPhase: 'accumulation' | 'markup' | 'distribution' | 'markdown' | 'unclear';
    sessionTime: 'london' | 'newyork' | 'asian' | 'overlap' | 'dead';
}

export interface EnhancedSignalStrength {
    buy: number;    // 0-100
    sell: number;   // 0-100
    confidence: number; // 0-100
    quality: 'A+' | 'A' | 'B' | 'C' | 'D'; // Signal quality grade
    confirmations: number; // Number of confirming indicators
    divergences: number; // Number of diverging indicators
}

export interface OptimizedAnalysis {
    signals: EnhancedSignalStrength;
    marketCondition: OptimizedMarketCondition;
    riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
    recommendation: 'strong_buy' | 'buy' | 'weak_buy' | 'hold' | 'weak_sell' | 'sell' | 'strong_sell';
    reasoning: string[];
    ictSetup: {
        hasSetup: boolean;
        setupType: 'bullish_ob' | 'bearish_ob' | 'fvg_fill' | 'liquidity_grab' | 'none';
        confidence: number;
    };
    entryTiming: 'immediate' | 'wait_for_pullback' | 'wait_for_breakout' | 'no_entry';
}

// Define a type for the configuration object
export type OptimizedSmartStrategyConfig = {
    minConfidence: number;
    minSignalStrength: number;
    minConfirmations: number;
    maxDivergences: number;
    maxRiskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
    maxConsecutiveLosses: number;
    maxDailyTrades: number;
    minTimeBetweenTrades: number;
    requiredTrendStrength: number;
    avoidHighVolatility: boolean;
    requireVolumeConfirmation: boolean;
    requireICTSetup: boolean;
    minICTConfidence: number;
    preferredSessions: ('london' | 'newyork' | 'asian' | 'overlap' | 'dead')[];
    avoidDeadHours: boolean;
    minQuality: 'A+' | 'A' | 'B' | 'C' | 'D';
};

export class OptimizedSmartStrategy implements TradingStrategy {
    private priceHistory: number[] = [];
    private highHistory: number[] = [];
    private lowHistory: number[] = [];
    private volumeHistory: number[] = [];
    private analysisHistory: OptimizedAnalysis[] = [];
    private lastTradeTime: number = 0;
    private consecutiveLosses: number = 0;
    private dailyTrades: number = 0;
    private lastResetDay: number = 0;
    
    // Optimalizovaná konfigurácia pre 85%+ win rate
    private config: OptimizedSmartStrategyConfig = {
        // Signal filtering
        minConfidence: 50,              // Relaxed minimum confidence
        minSignalStrength: 40,          // Relaxed minimum signal strength
        minConfirmations: 0,            // Relaxed minimum confirmations
        maxDivergences: 3,              // Max 3 diverging indicators
        
        // Risk management
        maxRiskLevel: 'medium',   // Allow medium risk
        maxConsecutiveLosses: 2,        // Max 2 losses in a row
        maxDailyTrades: 5,              // Max 5 trades per day
        minTimeBetweenTrades: 300000,   // Min 5 minutes between trades
        
        // Market conditions
        requiredTrendStrength: 0,      // No minimum trend strength for testing
        avoidHighVolatility: true,      // Avoid high volatility
        requireVolumeConfirmation: true, // Require volume confirmation
        
        // ICT concepts
        requireICTSetup: false,          // Require ICT setup
        minICTConfidence: 75,           // Min ICT confidence 75%
        
        // Session filtering
        preferredSessions: ['london', 'newyork', 'asian', 'overlap', 'dead'], // Include all sessions for testing
        avoidDeadHours: false, // Temporarily allow trading in dead hours for testing
        
        // Quality grades
        minQuality: 'C',  // Allow C grade signals for testing
    };

    decideAction(marketData: any): TradeOrder | null {
        console.log(`DEBUG: decideAction called with price: ${marketData.price}`);
        this.updateHistory(marketData);
        this.resetDailyCounters();
        
        // Potrebujeme dostatok dát pre analýzu
        if (this.priceHistory.length < 100) { 
            console.log(`DEBUG: decideAction - Not enough history data: ${this.priceHistory.length}`);
            return null;
        }

        // Kontrola denných limitov
        if (this.dailyTrades >= this.config.maxDailyTrades) {
            console.log(`DEBUG: decideAction - Daily trades limit reached: ${this.dailyTrades}/${this.config.maxDailyTrades}`);
            return null;
        }

        // Kontrola času medzi obchodmi
        const currentTime = Date.now();
        if (currentTime - this.lastTradeTime < this.config.minTimeBetweenTrades) {
            console.log(`DEBUG: decideAction - Time between trades too short: ${currentTime - this.lastTradeTime}ms < ${this.config.minTimeBetweenTrades}ms`);
            return null;
        }

        // Kontrola po stratách
        if (this.consecutiveLosses >= this.config.maxConsecutiveLosses) {
            console.log(`DEBUG: decideAction - Consecutive losses limit reached: ${this.consecutiveLosses}/${this.config.maxConsecutiveLosses}`);
            return null;
        }

        // Vykonaj optimalizovanú analýzu
        const analysis = this.performOptimizedAnalysis();
        this.analysisHistory.push(analysis);
        
        // Loguj analýzu
        this.logOptimizedAnalysis(analysis, marketData);
        
        // Rozhoduj na základe prísnych kritérií
        return this.makeOptimizedDecision(analysis);
    }

    private updateHistory(marketData: any): void {
        this.priceHistory.push(marketData.price);
        this.highHistory.push(marketData.high || marketData.price * 1.001);
        this.lowHistory.push(marketData.low || marketData.price * 0.999);
        this.volumeHistory.push(marketData.volume || 1000);
        
        // Udržuj optimálnu históriu (300 bodov pre lepšiu analýzu)
        const maxHistory = 300;
        if (this.priceHistory.length > maxHistory) {
            this.priceHistory = this.priceHistory.slice(-maxHistory);
            this.highHistory = this.highHistory.slice(-maxHistory);
            this.lowHistory = this.lowHistory.slice(-maxHistory);
            this.volumeHistory = this.volumeHistory.slice(-maxHistory);
        }
    }

    private resetDailyCounters(): void {
        const currentDay = Math.floor(Date.now() / (24 * 60 * 60 * 1000));
        if (currentDay !== this.lastResetDay) {
            this.dailyTrades = 0;
            this.lastResetDay = currentDay;
        }
    }

    private performOptimizedAnalysis(): OptimizedAnalysis {
        const signals = this.calculateEnhancedSignalStrength();
        const marketCondition = this.analyzeOptimizedMarketCondition();
        const riskLevel = this.assessOptimizedRiskLevel(marketCondition);
        const ictSetup = this.analyzeICTSetup();
        const entryTiming = this.determineEntryTiming(signals, marketCondition, ictSetup);
        const recommendation = this.generateOptimizedRecommendation(signals, marketCondition, riskLevel, ictSetup);
        const reasoning = this.generateOptimizedReasoning(signals, marketCondition, riskLevel, ictSetup);

        return {
            signals,
            marketCondition,
            riskLevel,
            recommendation,
            reasoning,
            ictSetup,
            entryTiming
        };
    }

    private calculateEnhancedSignalStrength(): EnhancedSignalStrength {
        if (this.priceHistory.length < 50) {
            return {
                buy: 0,
                sell: 0,
                confidence: 0,
                quality: 'D',
                confirmations: 0,
                divergences: 3
            };
        }

        // Skutočná analýza signálov
        const rsi = TechnicalIndicators.calculateRSI(this.priceHistory);
        const macd = TechnicalIndicators.calculateMACD(this.priceHistory);
        const bb = TechnicalIndicators.calculateBollingerBands(this.priceHistory);
        const trend = TechnicalIndicators.detectTrend(this.priceHistory);
        const sr = TechnicalIndicators.findSupportResistance(this.priceHistory);

        let buySignal = 0;
        let sellSignal = 0;
        let confirmations = 0;
        let divergences = 0;

        const currentPrice = this.priceHistory[this.priceHistory.length - 1];
        const currentRSI = rsi.values[rsi.values.length - 1];
        const currentMACD = macd.histogram[macd.histogram.length - 1];

        // RSI signály
        if (currentRSI < 30) {
            buySignal += 25;
            confirmations++;
        } else if (currentRSI > 70) {
            sellSignal += 25;
            confirmations++;
        } else {
            divergences++;
        }

        // MACD signály
        if (currentMACD > 0 && macd.histogram[macd.histogram.length - 2] <= 0) {
            buySignal += 30;
            confirmations++;
        } else if (currentMACD < 0 && macd.histogram[macd.histogram.length - 2] >= 0) {
            sellSignal += 30;
            confirmations++;
        } else {
            divergences++;
        }

        // Bollinger Bands signály
        const upperBand = bb.upper[bb.upper.length - 1];
        const lowerBand = bb.lower[bb.lower.length - 1];
        const middleBand = bb.middle[bb.middle.length - 1];

        if (currentPrice <= lowerBand) {
            buySignal += 20;
            confirmations++;
        } else if (currentPrice >= upperBand) {
            sellSignal += 20;
            confirmations++;
        } else {
            divergences++;
        }

        // Trend potvrdenie
        if (trend.trend === 'uptrend' && trend.strength > 60) {
            buySignal += 15;
            confirmations++;
        } else if (trend.trend === 'downtrend' && trend.strength > 60) {
            sellSignal += 15;
            confirmations++;
        } else {
            divergences++;
        }

        // Support/Resistance
        const nearSupport = sr.support.some(level => Math.abs(currentPrice - level) / currentPrice < 0.002);
        const nearResistance = sr.resistance.some(level => Math.abs(currentPrice - level) / currentPrice < 0.002);

        if (nearSupport) {
            buySignal += 10;
            confirmations++;
        } else if (nearResistance) {
            sellSignal += 10;
            confirmations++;
        }

        // Normalizácia signálov
        buySignal = Math.min(buySignal, 100);
        sellSignal = Math.min(sellSignal, 100);

        // Výpočet dôvery a kvality
        const totalSignalStrength = Math.max(buySignal, sellSignal);
        const confidence = Math.min((confirmations / (confirmations + divergences)) * 100, 100);
        
        let quality: 'A+' | 'A' | 'B' | 'C' | 'D';
        if (totalSignalStrength >= 80 && confidence >= 90) quality = 'A+';
        else if (totalSignalStrength >= 70 && confidence >= 80) quality = 'A';
        else if (totalSignalStrength >= 60 && confidence >= 70) quality = 'B';
        else if (totalSignalStrength >= 50 && confidence >= 60) quality = 'C';
        else quality = 'D';

        return {
            buy: buySignal,
            sell: sellSignal,
            confidence,
            quality,
            confirmations,
            divergences
        };
    }

    private analyzeOptimizedMarketCondition(): OptimizedMarketCondition {
        const trend = TechnicalIndicators.detectTrend(this.priceHistory);
        const atr = TechnicalIndicators.calculateATR(this.highHistory, this.lowHistory, this.priceHistory);
        const currentPrice = this.priceHistory[this.priceHistory.length - 1];
        
        // Enhanced volatility analysis
        const volatilityPercent = (atr / currentPrice) * 100;
        let volatility: 'low' | 'medium' | 'high';
        if (volatilityPercent < 0.8) volatility = 'low';
        else if (volatilityPercent < 2.5) volatility = 'medium';
        else volatility = 'high';

        // Enhanced momentum analysis
        const shortMomentum = this.calculateMomentum(5);
        const mediumMomentum = this.calculateMomentum(10);
        let momentum: 'strong' | 'weak' | 'neutral';
        
        if (Math.abs(shortMomentum) > 1.5 && Math.abs(mediumMomentum) > 1.0) momentum = 'strong';
        else if (Math.abs(shortMomentum) > 0.5 || Math.abs(mediumMomentum) > 0.3) momentum = 'weak';
        else momentum = 'neutral';

        // Volume analysis
        let volume: 'high' | 'low' | 'normal' = 'normal';
        if (this.volumeHistory.length >= 20) {
            const recentVolume = this.volumeHistory.slice(-5).reduce((sum, v) => sum + v, 0) / 5;
            const avgVolume = this.volumeHistory.slice(-20).reduce((sum, v) => sum + v, 0) / 20;
            const volumeRatio = recentVolume / avgVolume;
            
            if (volumeRatio > 1.8) volume = 'high';
            else if (volumeRatio < 0.6) volume = 'low';
        }

        // Market phase analysis (Wyckoff)
        const marketPhase = this.determineMarketPhase();
        
        // Session time analysis
        const sessionTime = this.getCurrentSession();

        return {
            trend: trend.trend,
            trendStrength: trend.strength,
            volatility,
            momentum,
            volume,
            marketPhase,
            sessionTime
        };
    }

    private assessOptimizedRiskLevel(marketCondition: OptimizedMarketCondition): 'very_low' | 'low' | 'medium' | 'high' | 'very_high' {
        let riskScore = 0;
        
        // Volatility risk
        if (marketCondition.volatility === 'high') riskScore += 3;
        else if (marketCondition.volatility === 'medium') riskScore += 1;
        
        // Trend strength risk
        if (marketCondition.trendStrength < 50) riskScore += 2;
        else if (marketCondition.trendStrength < 70) riskScore += 1;
        
        // Session risk
        if (marketCondition.sessionTime === 'dead') riskScore += 2;
        else if (marketCondition.sessionTime === 'asian') riskScore += 1;
        
        // Market phase risk
        if (marketCondition.marketPhase === 'distribution' || marketCondition.marketPhase === 'accumulation') {
            riskScore += 1;
        }
        
        // Volume risk
        if (marketCondition.volume === 'low') riskScore += 1;
        
        if (riskScore === 0) return 'very_low';
        else if (riskScore <= 2) return 'low';
        else if (riskScore <= 4) return 'medium';
        else if (riskScore <= 6) return 'high';
        else return 'very_high';
    }

    private analyzeICTSetup(): { hasSetup: boolean; setupType: 'bullish_ob' | 'bearish_ob' | 'fvg_fill' | 'liquidity_grab' | 'none'; confidence: number; } {
        // Simplified ICT analysis - would be expanded with full ICT implementation
        const recentPrices = this.priceHistory.slice(-20);
        const recentHighs = this.highHistory.slice(-20);
        const recentLows = this.lowHistory.slice(-20);
        
        // Look for order blocks
        const bullishOB = this.detectBullishOrderBlock(recentPrices, recentHighs, recentLows);
        const bearishOB = this.detectBearishOrderBlock(recentPrices, recentHighs, recentLows);
        
        // Look for Fair Value Gaps
        const fvg = this.detectFairValueGap(recentHighs, recentLows);
        
        // Look for liquidity grabs
        const liquidityGrab = this.detectLiquidityGrab(recentPrices, recentHighs, recentLows);
        
        if (bullishOB.detected && bullishOB.confidence > 70) {
            return { hasSetup: true, setupType: 'bullish_ob', confidence: bullishOB.confidence };
        } else if (bearishOB.detected && bearishOB.confidence > 70) {
            return { hasSetup: true, setupType: 'bearish_ob', confidence: bearishOB.confidence };
        } else if (fvg.detected && fvg.confidence > 70) {
            return { hasSetup: true, setupType: 'fvg_fill', confidence: fvg.confidence };
        } else if (liquidityGrab.detected && liquidityGrab.confidence > 70) {
            return { hasSetup: true, setupType: 'liquidity_grab', confidence: liquidityGrab.confidence };
        }
        
        return { hasSetup: false, setupType: 'none', confidence: 0 };
    }

    private makeOptimizedDecision(analysis: OptimizedAnalysis): TradeOrder | null {
        // Temporarily simplify decision logic to ensure trades are being placed
        // This is for debugging purposes to see if the strategy can generate any trades.

        // Check confidence and signal strength first
        if (analysis.signals.confidence < this.config.minConfidence ||
            (analysis.signals.buy < this.config.minSignalStrength && analysis.signals.sell < this.config.minSignalStrength)) {
            // console.log(`DEBUG: Decision blocked by low confidence or signal strength.`);
            return null;
        }

        // Check risk level
        const riskOrder = ['very_low', 'low', 'medium', 'high', 'very_high'];
        const currentRiskIndex = riskOrder.indexOf(analysis.riskLevel);
        const maxAllowedRiskIndex = riskOrder.indexOf(this.config.maxRiskLevel);

        if (currentRiskIndex > maxAllowedRiskIndex) {
            // console.log(`DEBUG: Decision blocked by risk level (${analysis.riskLevel} > ${this.config.maxRiskLevel})`);
            return null;
        }

        // Make decision based on recommendation
        if (analysis.recommendation === 'strong_buy' || analysis.recommendation === 'buy' || analysis.recommendation === 'weak_buy') {
            if (analysis.signals.buy > this.config.minSignalStrength) {
                this.lastTradeTime = Date.now();
                this.dailyTrades++;
                return { action: 'buy', amount: this.calculateOptimizedPositionSize(analysis) };
            }
        }
        
        if (analysis.recommendation === 'strong_sell' || analysis.recommendation === 'sell' || analysis.recommendation === 'weak_sell') {
            if (analysis.signals.sell > this.config.minSignalStrength) {
                this.lastTradeTime = Date.now();
                this.dailyTrades++;
                return { action: 'sell', amount: this.calculateOptimizedPositionSize(analysis) };
            }
        }
        
        return null;
    }

    private calculateOptimizedPositionSize(analysis: OptimizedAnalysis): number {
        let baseSize = 8; // Menšia základná veľkosť pre bezpečnosť
        
        // Quality multiplier
        const qualityMultiplier = analysis.signals.quality === 'A+' ? 1.3 : 
                                 analysis.signals.quality === 'A' ? 1.1 : 1.0;
        
        // Confidence multiplier
        const confidenceMultiplier = analysis.signals.confidence / 100;
        
        // Risk multiplier
        const riskMultiplier = analysis.riskLevel === 'very_low' ? 1.2 : 
                              analysis.riskLevel === 'low' ? 1.0 : 0.8;
        
        // ICT setup multiplier
        const ictMultiplier = analysis.ictSetup.hasSetup ? 1.1 : 1.0;
        
        // Trend strength multiplier
        const trendMultiplier = analysis.marketCondition.trendStrength / 100;
        
        const finalSize = Math.round(baseSize * qualityMultiplier * confidenceMultiplier * 
                                   riskMultiplier * ictMultiplier * trendMultiplier);
        
        return Math.max(finalSize, 3); // Minimálne 3 jednotky
    }

    // Helper methods (simplified implementations)
    private findRecentHighs(): number[] {
        // Find recent price highs within the last 50 periods
        const recentHighs = this.highHistory.slice(-50);
        if (recentHighs.length === 0) return [];
        
        // A simple approach: return the highest value in the recent history
        // For more advanced analysis, one might look for swing highs.
        return [Math.max(...recentHighs)];
    }

    private findRecentLows(): number[] {
        // Find recent price lows within the last 50 periods
        const recentLows = this.lowHistory.slice(-50);
        if (recentLows.length === 0) return [];
        
        // A simple approach: return the lowest value in the recent history
        // For more advanced analysis, one might look for swing lows.
        return [Math.min(...recentLows)];
    }

    private detectRSIDivergence(priceHighs: number[], priceLows: number[]): 'bullish' | 'bearish' | 'none' {
        if (this.priceHistory.length < 100) return 'none'; // Need enough data for RSI

        const rsiResult = TechnicalIndicators.calculateRSI(this.priceHistory);
        const rsiValues = rsiResult.values;
        if (!rsiValues || rsiValues.length < 20) return 'none'; // Need enough RSI values

        const recentRSI = rsiValues.slice(-20);
        const currentRSI = recentRSI[recentRSI.length - 1];
        const previousRSI = recentRSI[recentRSI.length - 2];

        // Simplified divergence detection:
        // Bullish Divergence: Price makes lower low, but RSI makes higher low
        // Bearish Divergence: Price makes higher high, but RSI makes lower high

        // To make this more robust, we'd need to identify swing points (peaks and troughs)
        // in both price and RSI, and then compare them.
        // For now, a very basic check:

        const currentPrice = this.priceHistory[this.priceHistory.length - 1];
        const previousPrice = this.priceHistory[this.priceHistory.length - 2];

        // Bullish Divergence (simplified): Price drops, but RSI rises
        if (currentPrice < previousPrice && currentRSI > previousRSI && priceLows.length > 1) {
            // Check if current price is a lower low than a previous significant low
            const lastTwoLows = priceLows.slice(-2);
            if (lastTwoLows.length === 2 && currentPrice < lastTwoLows[0]) {
                // And if RSI is making a higher low
                const rsiLows = TechnicalIndicators.findSupportResistance(rsiValues).support; // Using S/R for RSI lows
                if (rsiLows.length > 1 && currentRSI > rsiLows[rsiLows.length - 2]) {
                    return 'bullish';
                }
            }
        }

        // Bearish Divergence (simplified): Price rises, but RSI drops
        if (currentPrice > previousPrice && currentRSI < previousRSI && priceHighs.length > 1) {
            // Check if current price is a higher high than a previous significant high
            const lastTwoHighs = priceHighs.slice(-2);
            if (lastTwoHighs.length === 2 && currentPrice > lastTwoHighs[0]) {
                // And if RSI is making a lower high
                const rsiHighs = TechnicalIndicators.findSupportResistance(rsiValues).resistance; // Using S/R for RSI highs
                if (rsiHighs.length > 1 && currentRSI < rsiHighs[rsiHighs.length - 2]) {
                    return 'bearish';
                }
            }
        }

        return 'none';
    }

    private findStrongSupportResistance(levels: number[], currentPrice: number, type: 'support' | 'resistance'): { isNear: boolean; strength: number; } {
        if (levels.length === 0) return { isNear: false, strength: 0 };

        const tolerance = currentPrice * 0.005; // 0.5% tolerance
        let isNear = false;
        let strength = 0;

        for (const level of levels) {
            if (Math.abs(currentPrice - level) <= tolerance) {
                isNear = true;
                // Simple strength calculation: closer levels are stronger,
                // and more historical touches would increase strength.
                // For now, a basic strength based on proximity.
                strength = Math.max(strength, 100 - (Math.abs(currentPrice - level) / tolerance) * 100);
            }
        }
        return { isNear, strength: Math.min(strength, 100) };
    }

    private analyzeMarketStructure(): { structure: 'higher_highs_lows' | 'lower_highs_lows' | 'unclear'; } {
        if (this.priceHistory.length < 50) { // Need enough data to determine structure
            return { structure: 'unclear' };
        }

        const recentPrices = this.priceHistory.slice(-50);
        const highs = this.highHistory.slice(-50);
        const lows = this.lowHistory.slice(-50);

        // Simple approach: Compare recent highs and lows to determine structure
        const lastHigh = highs[highs.length - 1];
        const secondLastHigh = highs[highs.length - 2];
        const lastLow = lows[lows.length - 1];
        const secondLastLow = lows[lows.length - 2];

        if (lastHigh > secondLastHigh && lastLow > secondLastLow) {
            return { structure: 'higher_highs_lows' };
        } else if (lastHigh < secondLastHigh && lastLow < secondLastLow) {
            return { structure: 'lower_highs_lows' };
        } else {
            return { structure: 'unclear' };
        }
    }

    private calculateMomentum(period: number): number {
        if (this.priceHistory.length < period + 1) return 0;
        const current = this.priceHistory[this.priceHistory.length - 1];
        const previous = this.priceHistory[this.priceHistory.length - 1 - period];
        return ((current - previous) / previous) * 100;
    }

    private determineMarketPhase(): 'accumulation' | 'markup' | 'distribution' | 'markdown' | 'unclear' {
        // This is a simplified Wyckoff phase analysis.
        // A full implementation would require more complex logic involving volume, price action,
        // and structural analysis (e.g., springs, upthrusts, tests).
        // For now, we'll use a basic trend-based approach.

        if (this.priceHistory.length < 100) return 'markup'; // Default if not enough data

        const longTermTrend = TechnicalIndicators.detectTrend(this.priceHistory.slice(-100));
        const mediumTermTrend = TechnicalIndicators.detectTrend(this.priceHistory.slice(-50));
        const shortTermTrend = TechnicalIndicators.detectTrend(this.priceHistory.slice(-20));

        if (longTermTrend.trend === 'uptrend' && mediumTermTrend.trend === 'uptrend' && shortTermTrend.trend === 'uptrend') {
            return 'markup'; // Strong uptrend
        } else if (longTermTrend.trend === 'downtrend' && mediumTermTrend.trend === 'downtrend' && shortTermTrend.trend === 'downtrend') {
            return 'markdown'; // Strong downtrend
        } else if (longTermTrend.trend === 'sideways' && mediumTermTrend.trend === 'sideways' && shortTermTrend.trend === 'sideways') {
            // If sideways across all timeframes, could be accumulation or distribution
            // Need more advanced logic to differentiate. For simplicity, assume accumulation if coming from markdown, distribution if from markup.
            // This requires tracking previous phase, which is not currently implemented.
            return 'accumulation'; // Placeholder, needs refinement
        } else if (shortTermTrend.trend === 'downtrend' && (longTermTrend.trend === 'uptrend' || mediumTermTrend.trend === 'uptrend')) {
            return 'distribution'; // Short-term downtrend in a larger uptrend (potential distribution)
        } else if (shortTermTrend.trend === 'uptrend' && (longTermTrend.trend === 'downtrend' || mediumTermTrend.trend === 'downtrend')) {
            return 'accumulation'; // Short-term uptrend in a larger downtrend (potential accumulation)
        }

        return 'unclear'; // Fallback for complex or unclear scenarios
    }

    private getCurrentSession(): 'london' | 'newyork' | 'asian' | 'overlap' | 'dead' {
        const now = new Date();
        const utcHour = now.getUTCHours();

        // Define session hours in UTC
        // Asian Session: 00:00 - 09:00 UTC
        // London Session: 08:00 - 17:00 UTC
        // New York Session: 13:00 - 22:00 UTC

        const isAsian = utcHour >= 0 && utcHour < 9;
        const isLondon = utcHour >= 8 && utcHour < 17;
        const isNewYork = utcHour >= 13 && utcHour < 22;

        if (isLondon && isNewYork) {
            return 'overlap'; // London and New York overlap
        } else if (isLondon) {
            return 'london';
        } else if (isNewYork) {
            return 'newyork';
        } else if (isAsian) {
            return 'asian';
        } else {
            return 'dead'; // Outside major session hours
        }
    }

    private detectBullishOrderBlock(prices: number[], highs: number[], lows: number[]): { detected: boolean; confidence: number; } {
        // A bullish order block is typically a down candle (or series of down candles)
        // before an impulsive move up that breaks market structure.
        // Simplified detection: Look for a recent low followed by a strong upward move.
        if (prices.length < 5) return { detected: false, confidence: 0 };

        const lastCandleClose = prices[prices.length - 1];
        const lastCandleOpen = prices[prices.length - 2]; // Assuming previous close is current open for simplicity
        const secondLastCandleClose = prices[prices.length - 2];
        const thirdLastCandleClose = prices[prices.length - 3];

        // Check for a down candle (or series) followed by a strong up move
        const isDownCandle = lastCandleClose < secondLastCandleClose;
        const strongUpMove = (lastCandleClose - thirdLastCandleClose) / thirdLastCandleClose > 0.005; // 0.5% move

        if (isDownCandle && strongUpMove) {
            // Further refinement would involve checking for market structure break,
            // volume confirmation, and specific candle body/wick characteristics.
            return { detected: true, confidence: 75 }; // Placeholder confidence
        }
        return { detected: false, confidence: 0 };
    }

    private detectBearishOrderBlock(prices: number[], highs: number[], lows: number[]): { detected: boolean; confidence: number; } {
        // A bearish order block is typically an up candle (or series of up candles)
        // before an impulsive move down that breaks market structure.
        // Simplified detection: Look for a recent high followed by a strong downward move.
        if (prices.length < 5) return { detected: false, confidence: 0 };

        const lastCandleClose = prices[prices.length - 1];
        const lastCandleOpen = prices[prices.length - 2]; // Assuming previous close is current open for simplicity
        const secondLastCandleClose = prices[prices.length - 2];
        const thirdLastCandleClose = prices[prices.length - 3];

        // Check for an up candle (or series) followed by a strong down move
        const isUpCandle = lastCandleClose > secondLastCandleClose;
        const strongDownMove = (thirdLastCandleClose - lastCandleClose) / thirdLastCandleClose > 0.005; // 0.5% move

        if (isUpCandle && strongDownMove) {
            // Further refinement would involve checking for market structure break,
            // volume confirmation, and specific candle body/wick characteristics.
            return { detected: true, confidence: 75 }; // Placeholder confidence
        }
        return { detected: false, confidence: 0 };
    }

    private detectFairValueGap(highs: number[], lows: number[]): { detected: boolean; confidence: number; } {
        // FVG (Fair Value Gap) is an inefficiency in price delivery,
        // typically a gap between the high of the first candle and the low of the third candle
        // (or vice versa for bearish FVG).
        if (highs.length < 3 || lows.length < 3) return { detected: false, confidence: 0 };

        const high1 = highs[highs.length - 3];
        const low1 = lows[lows.length - 3];
        const high2 = highs[highs.length - 2];
        const low2 = lows[lows.length - 2];
        const high3 = highs[highs.length - 1];
        const low3 = lows[lows.length - 1];

        // Bullish FVG: Low of candle 3 > High of candle 1
        if (low3 > high1) {
            return { detected: true, confidence: 80 };
        }
        // Bearish FVG: High of candle 3 < Low of candle 1
        if (high3 < low1) {
            return { detected: true, confidence: 80 };
        }
        return { detected: false, confidence: 0 };
    }

    private detectLiquidityGrab(prices: number[], highs: number[], lows: number[]): { detected: boolean; confidence: number; } {
        // Liquidity grab (or stop hunt) occurs when price briefly moves beyond a significant
        // high or low (where stop losses are likely to be placed) and then reverses sharply.
        if (prices.length < 10) return { detected: false, confidence: 0 };

        const currentPrice = prices[prices.length - 1];
        const recentHighs = this.findRecentHighs(); // Use the implemented helper
        const recentLows = this.findRecentLows();   // Use the implemented helper

        // Check for bullish liquidity grab (price dips below a low and reverses)
        if (recentLows.length > 0) {
            const lastSignificantLow = recentLows[0]; // Assuming findRecentLows returns the most recent significant low
            if (currentPrice > lastSignificantLow && prices[prices.length - 2] < lastSignificantLow) {
                // Price dipped below the low and then moved back above it
                return { detected: true, confidence: 85 };
            }
        }

        // Check for bearish liquidity grab (price pushes above a high and reverses)
        if (recentHighs.length > 0) {
            const lastSignificantHigh = recentHighs[0]; // Assuming findRecentHighs returns the most recent significant high
            if (currentPrice < lastSignificantHigh && prices[prices.length - 2] > lastSignificantHigh) {
                // Price pushed above the high and then moved back below it
                return { detected: true, confidence: 85 };
            }
        }
        return { detected: false, confidence: 0 };
    }

    private determineEntryTiming(signals: EnhancedSignalStrength, marketCondition: OptimizedMarketCondition, ictSetup: any): 'immediate' | 'wait_for_pullback' | 'wait_for_breakout' | 'no_entry' {
        if (signals.quality === 'A+' && marketCondition.momentum === 'strong') {
            return 'immediate';
        } else if (signals.quality === 'A' && ictSetup.hasSetup) {
            return 'wait_for_pullback';
        } else if (signals.confidence > 80) {
            return 'wait_for_breakout';
        }
        return 'no_entry';
    }

    private generateOptimizedRecommendation(
        signals: EnhancedSignalStrength,
        marketCondition: OptimizedMarketCondition,
        riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high',
        ictSetup: any
    ): 'strong_buy' | 'buy' | 'weak_buy' | 'hold' | 'weak_sell' | 'sell' | 'strong_sell' {
        
        // Ak je riziko príliš vysoké, drž
        if (riskLevel === 'high' || riskLevel === 'very_high') return 'hold';
        
        // Ak je dôvera nízka, drž
        if (signals.confidence < this.config.minConfidence) return 'hold';
        
        // Ak je kvalita signálu nízka, drž
        const qualityOrder = ['D', 'C', 'B', 'A', 'A+'];
        const currentQualityIndex = qualityOrder.indexOf(signals.quality);
        const minRequiredQualityIndex = qualityOrder.indexOf(this.config.minQuality);

        if (currentQualityIndex < minRequiredQualityIndex) return 'hold';
        
        // Silné signály s vysokou dôverou a kvalitou A+
        if (signals.quality === 'A+' && signals.confidence > 90) {
            if (signals.buy > signals.sell && signals.buy > 85) return 'strong_buy';
            if (signals.sell > signals.buy && signals.sell > 85) return 'strong_sell';
        }
        
        // Dobré signály s kvalitou A
        if (signals.quality === 'A' && signals.confidence > this.config.minConfidence) {
            if (signals.buy > signals.sell && signals.buy > this.config.minSignalStrength) {
                return ictSetup.hasSetup ? 'buy' : 'weak_buy';
            }
            if (signals.sell > signals.buy && signals.sell > this.config.minSignalStrength) {
                return ictSetup.hasSetup ? 'sell' : 'weak_sell';
            }
        }
        
        return 'hold';
    }

    private generateOptimizedReasoning(
        signals: EnhancedSignalStrength,
        marketCondition: OptimizedMarketCondition,
        riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high',
        ictSetup: any
    ): string[] {
        const reasoning: string[] = [];
        
        reasoning.push(`Kvalita signálu: ${signals.quality} (dôvera: ${signals.confidence.toFixed(1)}%)`);
        reasoning.push(`Potvrdenia: ${signals.confirmations}, Divergencie: ${signals.divergences}`);
        reasoning.push(`Buy sila: ${signals.buy.toFixed(1)}%, Sell sila: ${signals.sell.toFixed(1)}%`);
        reasoning.push(`Trend: ${marketCondition.trend} (sila: ${marketCondition.trendStrength.toFixed(1)}%)`);
        reasoning.push(`Volatilita: ${marketCondition.volatility}, Momentum: ${marketCondition.momentum}`);
        reasoning.push(`Volume: ${marketCondition.volume}, Session: ${marketCondition.sessionTime}`);
        reasoning.push(`Market Phase: ${marketCondition.marketPhase}`);
        reasoning.push(`Úroveň rizika: ${riskLevel}`);
        
        if (ictSetup.hasSetup) {
            reasoning.push(`ICT Setup: ${ictSetup.setupType} (${ictSetup.confidence.toFixed(1)}% dôvera)`);
        } else {
            reasoning.push('ICT Setup: Žiadny setup detekovaný');
        }
        
        return reasoning;
    }

    private logOptimizedAnalysis(analysis: OptimizedAnalysis, marketData: any): void {
        // Loguj len významné zmeny alebo obchody
        const shouldLogDetailed = analysis.recommendation !== 'hold' || 
                                 analysis.signals.quality === 'A+' || 
                                 analysis.signals.quality === 'A' ||
                                 this.analysisHistory.length % 50 === 0; // Každých 50 analýz

        if (shouldLogDetailed) {
            console.log(`🧠 === OPTIMALIZOVANÁ SMART ANALÝZA ===`);
            console.log(`💹 Cena: ${marketData.price}`);
            console.log(`🎯 Odporúčanie: ${analysis.recommendation.toUpperCase()}`);
            console.log(`⭐ Kvalita: ${analysis.signals.quality} (${analysis.signals.confidence.toFixed(1)}% dôvera)`);
            console.log(`📊 Buy: ${analysis.signals.buy.toFixed(1)}%, Sell: ${analysis.signals.sell.toFixed(1)}%`);
            console.log(`✅ Potvrdenia: ${analysis.signals.confirmations}, ❌ Divergencie: ${analysis.signals.divergences}`);
            console.log(`⚠️ Riziko: ${analysis.riskLevel}`);
            console.log(`📈 Trend: ${analysis.marketCondition.trend} (${analysis.marketCondition.trendStrength.toFixed(1)}%)`);
            console.log(`🕐 Session: ${analysis.marketCondition.sessionTime}, Phase: ${analysis.marketCondition.marketPhase}`);
            console.log(`⏰ Entry Timing: ${analysis.entryTiming}`);
            
            if (analysis.ictSetup.hasSetup) {
                console.log(`🎯 ICT Setup: ${analysis.ictSetup.setupType} (${analysis.ictSetup.confidence.toFixed(1)}%)`);
            }
            
            console.log(`📋 Dôvody:`);
            analysis.reasoning.forEach(reason => console.log(`     • ${reason}`));
            
            console.log(`════════════════════════════════════════════════════════════════`);
        } else {
            // Krátky log pre HOLD signály
            console.log(`📊 ${marketData.price} | ${analysis.recommendation.toUpperCase()} | ${analysis.signals.quality}(${analysis.signals.confidence.toFixed(0)}%) | ${analysis.riskLevel}`);
        }
    }

    // Getter pre štatistiky
    getOptimizedAnalysisHistory(): OptimizedAnalysis[] {
        return [...this.analysisHistory];
    }

    getOptimizedWinRateStats(): {
        totalSignals: number;
        qualityAPlus: number;
        qualityA: number;
        avgConfidence: number;
        avgConfirmations: number;
    } {
        const history = this.analysisHistory;
        const qualityAPlus = history.filter(a => a.signals.quality === 'A+').length;
        const qualityA = history.filter(a => a.signals.quality === 'A').length;
        const avgConfidence = history.length > 0 ?
            history.reduce((sum, a) => sum + a.signals.confidence, 0) / history.length : 0;
        const avgConfirmations = history.length > 0 ?
            history.reduce((sum, a) => sum + a.signals.confirmations, 0) / history.length : 0;

        return {
            totalSignals: history.length,
            qualityAPlus,
            qualityA,
            avgConfidence,
            avgConfirmations
        };
    }

    // Metóda na reset po strate
    recordTradeLoss(): void {
        this.consecutiveLosses++;
    }

    // Metóda na reset po výhre
    recordTradeWin(): void {
        this.consecutiveLosses = 0;
    }

    // Metóda na nastavenie konfigurácie z optimalizácie
    public setConfig(newConfig: OptimizedSmartStrategyConfig): void {
        Object.assign(this.config, newConfig);
    }
}
