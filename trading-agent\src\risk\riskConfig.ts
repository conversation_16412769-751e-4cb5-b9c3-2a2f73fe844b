// src/risk/riskConfig.ts

import { RiskConfig } from './riskManager.js';

// Konzervatívna konfigurácia (pre začiatočn<PERSON>ov) - OPTIMALIZOVANÁ PRE ÚSPECH
export const conservativeRiskConfig: RiskConfig = {
    maxRiskPerTrade: 1.5,    // 1.5% z účtu na jeden obchod (zvýšené pre viac obchodov)
    maxDailyLoss: 5.0,       // 5% maximálna denná strata (zvýšené)
    maxDrawdown: 12.0,       // 12% maximálny drawdown (zvýšené)
    stopLossPercent: 1.2,    // 1.2% stop loss (realistickejšie pre forex)
    takeProfitPercent: 2.0,  // 2% take profit (lepší R:R)
    riskRewardRatio: 1.5     // 1:1.5 risk/reward ratio (realistickejšie)
};

// Stredná konfigurácia (pre pokročilých)
export const moderateRiskConfig: RiskConfig = {
    maxRiskPerTrade: 2.0,    // 2% z účtu na jeden obchod
    maxDailyLoss: 5.0,       // 5% maximálna denná strata
    maxDrawdown: 12.0,       // 12% maximálny drawdown
    stopLossPercent: 1.0,    // 1% stop loss
    takeProfitPercent: 2.0,  // 2% take profit
    riskRewardRatio: 2.0     // 1:2 risk/reward ratio
};

// Agresívna konfigurácia (pre expertov)
export const aggressiveRiskConfig: RiskConfig = {
    maxRiskPerTrade: 3.0,    // 3% z účtu na jeden obchod
    maxDailyLoss: 8.0,       // 8% maximálna denná strata
    maxDrawdown: 15.0,       // 15% maximálny drawdown
    stopLossPercent: 1.5,    // 1.5% stop loss
    takeProfitPercent: 3.0,  // 3% take profit
    riskRewardRatio: 2.0     // 1:2 risk/reward ratio
};

// Scalping konfigurácia (pre vysokofrekvenčné obchodovanie)
export const scalpingRiskConfig: RiskConfig = {
    maxRiskPerTrade: 0.5,    // 0.5% z účtu na jeden obchod
    maxDailyLoss: 2.0,       // 2% maximálna denná strata
    maxDrawdown: 5.0,        // 5% maximálny drawdown
    stopLossPercent: 0.2,    // 0.2% stop loss
    takeProfitPercent: 0.4,  // 0.4% take profit
    riskRewardRatio: 2.0     // 1:2 risk/reward ratio
};

// Swing trading konfigurácia (pre dlhodobé pozície)
export const swingRiskConfig: RiskConfig = {
    maxRiskPerTrade: 2.5,    // 2.5% z účtu na jeden obchod
    maxDailyLoss: 6.0,       // 6% maximálna denná strata
    maxDrawdown: 20.0,       // 20% maximálny drawdown
    stopLossPercent: 2.0,    // 2% stop loss
    takeProfitPercent: 5.0,  // 5% take profit
    riskRewardRatio: 2.5     // 1:2.5 risk/reward ratio
};

// Funkcia na výber konfigurácie podľa typu obchodníka
export function getRiskConfigByType(traderType: 'conservative' | 'moderate' | 'aggressive' | 'scalping' | 'swing'): RiskConfig {
    switch (traderType) {
        case 'conservative':
            return conservativeRiskConfig;
        case 'moderate':
            return moderateRiskConfig;
        case 'aggressive':
            return aggressiveRiskConfig;
        case 'scalping':
            return scalpingRiskConfig;
        case 'swing':
            return swingRiskConfig;
        default:
            return moderateRiskConfig;
    }
}

// Funkcia na validáciu risk konfigurácie
export function validateRiskConfig(config: RiskConfig): string[] {
    const errors: string[] = [];

    if (config.maxRiskPerTrade <= 0 || config.maxRiskPerTrade > 10) {
        errors.push('maxRiskPerTrade musí byť medzi 0.1% a 10%');
    }

    if (config.maxDailyLoss <= 0 || config.maxDailyLoss > 20) {
        errors.push('maxDailyLoss musí byť medzi 0.1% a 20%');
    }

    if (config.maxDrawdown <= 0 || config.maxDrawdown > 50) {
        errors.push('maxDrawdown musí byť medzi 0.1% a 50%');
    }

    if (config.stopLossPercent <= 0 || config.stopLossPercent > 10) {
        errors.push('stopLossPercent musí byť medzi 0.01% a 10%');
    }

    if (config.takeProfitPercent <= 0 || config.takeProfitPercent > 20) {
        errors.push('takeProfitPercent musí byť medzi 0.01% a 20%');
    }

    if (config.riskRewardRatio < 1 || config.riskRewardRatio > 10) {
        errors.push('riskRewardRatio musí byť medzi 1:1 a 1:10');
    }

    // Kontrola logických vzťahov
    if (config.takeProfitPercent / config.stopLossPercent < config.riskRewardRatio) {
        errors.push(`Take profit/Stop loss ratio (${(config.takeProfitPercent / config.stopLossPercent).toFixed(2)}) je menší ako požadovaný risk/reward ratio (${config.riskRewardRatio})`);
    }

    if (config.maxDailyLoss < config.maxRiskPerTrade) {
        errors.push('maxDailyLoss by mal byť väčší ako maxRiskPerTrade');
    }

    return errors;
}
