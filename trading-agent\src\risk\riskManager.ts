// src/risk/riskManager.ts

export interface RiskConfig {
    maxRiskPerTrade: number; // % z účtu na jeden obchod (napr. 2%)
    maxDailyLoss: number; // % maximálna denná strata (napr. 5%)
    maxDrawdown: number; // % maximálny drawdown (napr. 10%)
    stopLossPercent: number; // % stop loss (napr. 1%)
    takeProfitPercent: number; // % take profit (napr. 2%)
    riskRewardRatio: number; // minimálny risk/reward ratio (napr. 1:2)
}

export interface Position {
    symbol: string;
    amount: number;
    entryPrice: number;
    currentPrice: number;
    type: 'buy' | 'sell';
    stopLoss?: number;
    takeProfit?: number;
    timestamp: Date;
}

export interface RiskMetrics {
    currentDrawdown: number;
    dailyPnL: number;
    totalPnL: number;
    winRate: number;
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
}

export class RiskManager {
    private config: RiskConfig;
    private initialBalance: number;
    private currentBalance: number;
    private positions: Position[] = [];
    private tradeHistory: any[] = [];
    private dailyStartBalance: number;
    private maxBalanceReached: number;

    constructor(initialBalance: number, config: RiskConfig) {
        this.initialBalance = initialBalance;
        this.currentBalance = initialBalance;
        this.dailyStartBalance = initialBalance;
        this.maxBalanceReached = initialBalance;
        this.config = config;
    }

    // Vypočíta veľkosť pozície na základe rizika
    calculatePositionSize(entryPrice: number, stopLossPrice: number): number {
        const riskAmount = this.currentBalance * (this.config.maxRiskPerTrade / 100);
        const priceRisk = Math.abs(entryPrice - stopLossPrice);
        
        if (priceRisk === 0) return 0;
        
        return Math.floor(riskAmount / priceRisk);
    }

    // Vypočíta stop loss cenu
    calculateStopLoss(entryPrice: number, tradeType: 'buy' | 'sell'): number {
        const stopLossPercent = this.config.stopLossPercent / 100;
        
        if (tradeType === 'buy') {
            return entryPrice * (1 - stopLossPercent);
        } else {
            return entryPrice * (1 + stopLossPercent);
        }
    }

    // Vypočíta take profit cenu
    calculateTakeProfit(entryPrice: number, tradeType: 'buy' | 'sell'): number {
        const takeProfitPercent = this.config.takeProfitPercent / 100;
        
        if (tradeType === 'buy') {
            return entryPrice * (1 + takeProfitPercent);
        } else {
            return entryPrice * (1 - takeProfitPercent);
        }
    }

    // Skontroluje, či je obchod povolený
    canTrade(): boolean {
        const currentDrawdown = this.getCurrentDrawdown();
        const dailyLoss = this.getDailyLoss();

        // Kontrola maximálneho drawdown
        if (currentDrawdown >= this.config.maxDrawdown) {
            console.log(`❌ Obchodovanie zastavené: Drawdown ${currentDrawdown.toFixed(2)}% prekročil limit ${this.config.maxDrawdown}%`);
            return false;
        }

        // Kontrola dennej straty
        if (dailyLoss >= this.config.maxDailyLoss) {
            console.log(`❌ Obchodovanie zastavené: Denná strata ${dailyLoss.toFixed(2)}% prekročila limit ${this.config.maxDailyLoss}%`);
            return false;
        }

        return true;
    }

    // Validuje obchod pred vykonaním
    validateTrade(entryPrice: number, stopLoss: number, takeProfit: number, tradeType: 'buy' | 'sell'): boolean {
        const riskAmount = Math.abs(entryPrice - stopLoss);
        const rewardAmount = Math.abs(takeProfit - entryPrice);
        
        // Kontrola, že risk a reward nie sú nulové
        if (riskAmount === 0 || rewardAmount === 0) {
            console.log(`❌ Obchod zamietnutý: Neplatný risk (${riskAmount}) alebo reward (${rewardAmount})`);
            return false;
        }
        
        const actualRiskRewardRatio = rewardAmount / riskAmount;

        // Znížené požiadavky na R:R ratio pre lepšiu obchodovateľnosť
        const minRiskRewardRatio = Math.max(this.config.riskRewardRatio * 0.7, 1.0); // Minimálne 1:1, ale preferujeme konfiguráciu

        if (actualRiskRewardRatio < minRiskRewardRatio) {
            console.log(`❌ Obchod zamietnutý: Risk/Reward ratio ${actualRiskRewardRatio.toFixed(2)} je menší ako požadovaný ${minRiskRewardRatio.toFixed(2)}`);
            return false;
        }

        console.log(`✅ Obchod validovaný: R:R ratio ${actualRiskRewardRatio.toFixed(2)} (požadované: ${minRiskRewardRatio.toFixed(2)})`);
        return true;
    }

    // Pridá novú pozíciu
    addPosition(symbol: string, amount: number, entryPrice: number, tradeType: 'buy' | 'sell'): Position {
        const stopLoss = this.calculateStopLoss(entryPrice, tradeType);
        const takeProfit = this.calculateTakeProfit(entryPrice, tradeType);

        const position: Position = {
            symbol,
            amount,
            entryPrice,
            currentPrice: entryPrice,
            type: tradeType,
            stopLoss,
            takeProfit,
            timestamp: new Date()
        };

        this.positions.push(position);
        console.log(`📈 Pozícia otvorená: ${tradeType.toUpperCase()} ${amount} ${symbol} @ ${entryPrice}, SL: ${stopLoss.toFixed(4)}, TP: ${takeProfit.toFixed(4)}`);
        
        return position;
    }

    // Aktualizuje ceny pozícií a kontroluje stop loss/take profit
    updatePositions(currentPrices: { [symbol: string]: number }): void {
        for (let i = this.positions.length - 1; i >= 0; i--) {
            const position = this.positions[i];
            const currentPrice = currentPrices[position.symbol];
            
            if (!currentPrice) continue;
            
            position.currentPrice = currentPrice;

            // Kontrola stop loss
            if (this.shouldTriggerStopLoss(position)) {
                this.closePosition(i, position.stopLoss!, 'Stop Loss');
                continue;
            }

            // Kontrola take profit
            if (this.shouldTriggerTakeProfit(position)) {
                this.closePosition(i, position.takeProfit!, 'Take Profit');
                continue;
            }
        }
    }

    private shouldTriggerStopLoss(position: Position): boolean {
        if (!position.stopLoss) return false;
        
        if (position.type === 'buy') {
            return position.currentPrice <= position.stopLoss;
        } else {
            return position.currentPrice >= position.stopLoss;
        }
    }

    private shouldTriggerTakeProfit(position: Position): boolean {
        if (!position.takeProfit) return false;
        
        if (position.type === 'buy') {
            return position.currentPrice >= position.takeProfit;
        } else {
            return position.currentPrice <= position.takeProfit;
        }
    }

    private closePosition(index: number, exitPrice: number, reason: string): void {
        const position = this.positions[index];
        const pnl = this.calculatePnL(position, exitPrice);
        
        this.currentBalance += pnl;
        this.maxBalanceReached = Math.max(this.maxBalanceReached, this.currentBalance);

        // Pridaj do histórie
        this.tradeHistory.push({
            ...position,
            exitPrice,
            pnl,
            reason,
            exitTime: new Date()
        });

        console.log(`🔒 Pozícia zatvorená: ${reason} - P&L: ${pnl.toFixed(2)}, Nový zostatok: ${this.currentBalance.toFixed(2)}`);
        
        this.positions.splice(index, 1);
    }

    private calculatePnL(position: Position, exitPrice: number): number {
        const priceDiff = position.type === 'buy' 
            ? exitPrice - position.entryPrice 
            : position.entryPrice - exitPrice;
        
        return priceDiff * position.amount;
    }

    // Získa aktuálny drawdown
    getCurrentDrawdown(): number {
        return ((this.maxBalanceReached - this.currentBalance) / this.maxBalanceReached) * 100;
    }

    // Získa dennú stratu
    getDailyLoss(): number {
        const dailyPnL = this.currentBalance - this.dailyStartBalance;
        return dailyPnL < 0 ? Math.abs((dailyPnL / this.dailyStartBalance) * 100) : 0;
    }

    // Získa risk metriky
    getRiskMetrics(): RiskMetrics {
        const totalTrades = this.tradeHistory.length;
        const winningTrades = this.tradeHistory.filter(trade => trade.pnl > 0).length;
        const losingTrades = totalTrades - winningTrades;
        const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
        const totalPnL = this.currentBalance - this.initialBalance;
        const dailyPnL = this.currentBalance - this.dailyStartBalance;

        return {
            currentDrawdown: this.getCurrentDrawdown(),
            dailyPnL,
            totalPnL,
            winRate,
            totalTrades,
            winningTrades,
            losingTrades
        };
    }

    // Reset denného zostatku (volať na začiatku každého dňa)
    resetDailyBalance(): void {
        this.dailyStartBalance = this.currentBalance;
    }

    // Získa aktuálny zostatok
    getCurrentBalance(): number {
        return this.currentBalance;
    }

    // Získa otvorené pozície
    getOpenPositions(): Position[] {
        return [...this.positions];
    }

    // Získa históriu obchodov
    getTradeHistory(): any[] {
        return [...this.tradeHistory];
    }
}