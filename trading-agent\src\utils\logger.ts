// src/utils/logger.ts
export class Logger {
  private static instance: Logger;
  private messageCache: Map<string, number> = new Map();
  private readonly RATE_LIMIT_MS = 5000; // 5 sekúnd medzi rovnak<PERSON><PERSON> správami

  private constructor() {}

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private shouldLog(message: string): boolean {
    const now = Date.now();
    const lastLogged = this.messageCache.get(message);
    
    if (!lastLogged || (now - lastLogged) > this.RATE_LIMIT_MS) {
      this.messageCache.set(message, now);
      return true;
    }
    return false;
  }

  public info(message: string, ...args: any[]): void {
    if (this.shouldLog(message)) {
      console.log(`[${new Date().toISOString()}] [INFO] ${message}`, ...args);
    }
  }

  public warn(message: string, ...args: any[]): void {
    if (this.shouldLog(message)) {
      console.warn(`[${new Date().toISOString()}] [WARN] ${message}`, ...args);
    }
  }

  public error(message: string, ...args: any[]): void {
    if (this.shouldLog(message)) {
      console.error(`[${new Date().toISOString()}] [ERROR] ${message}`, ...args);
    }
  }

  public debug(message: string, ...args: any[]): void {
    // Only log debug messages in development environments
    if (process.env.NODE_ENV === 'development' && this.shouldLog(message)) {
      console.debug(`[${new Date().toISOString()}] [DEBUG] ${message}`, ...args);
    }
  }

  // Metóda pre vynútenie logovania (bez rate limiting)
  public forceLog(level: 'info' | 'warn' | 'error' | 'debug', message: string, ...args: any[]): void {
    const timestamp = new Date().toISOString();
    switch (level) {
      case 'info':
        console.log(`[${timestamp}] [INFO] ${message}`, ...args);
        break;
      case 'warn':
        console.warn(`[${timestamp}] [WARN] ${message}`, ...args);
        break;
      case 'error':
        console.error(`[${timestamp}] [ERROR] ${message}`, ...args);
        break;
      case 'debug':
        console.debug(`[${timestamp}] [DEBUG] ${message}`, ...args);
        break;
    }
  }
}

export const logger = Logger.getInstance();