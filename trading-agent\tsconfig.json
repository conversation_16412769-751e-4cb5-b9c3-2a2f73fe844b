{"compilerOptions": {"target": "es2020", "module": "NodeNext", "moduleResolution": "NodeNext", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "outDir": "./dist", "rootDir": ".", "declaration": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "allowImportingTsExtensions": false, "noEmit": false}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "build", "out", "node_modules/@modelcontextprotocol/sdk/dist/esm/server/index.d.ts"]}